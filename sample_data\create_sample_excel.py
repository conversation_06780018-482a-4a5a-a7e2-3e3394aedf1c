#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建示例Excel文件用于测试新租赁4+2功能
"""

import pandas as pd
from datetime import datetime, timedelta
import os

def create_sample_excel():
    """创建示例Excel文件"""
    
    # 确保目录存在
    os.makedirs('sample_data', exist_ok=True)
    
    # 订单管理数据
    order_data = [
        {
            '订单编号': 'ORD001',
            '订单日期': '2024-01-15',
            '客户姓名': '张三',
            '店铺归属': '北京店',
            '产品类型': '租赁',
            '台数': 1,
            '期数': 4,  # 新租赁4期
            '每期还款金': 800,
            '合同金额': 5200,
            '第1期': '2024-02-15',
            '第2期': '2024-03-15',
            '第3期': '2024-04-15',
            '第4期': '2024-05-15',
            '备注': '新租赁4+2测试订单'
        },
        {
            '订单编号': 'ORD002',
            '订单日期': '2024-01-20',
            '客户姓名': '李四',
            '店铺归属': '上海店',
            '产品类型': '租赁',
            '台数': 1,
            '期数': 6,  # 旧租赁6期
            '每期还款金': 600,
            '合同金额': 3900,
            '第1期': '2024-02-20',
            '第2期': '2024-03-20',
            '第3期': '2024-04-20',
            '第4期': '2024-05-20',
            '第5期': '2024-06-20',
            '第6期': '2024-07-20',
            '备注': '旧租赁6期测试订单'
        },
        {
            '订单编号': 'ORD003',
            '订单日期': '2024-01-25',
            '客户姓名': '王五',
            '店铺归属': '广州店',
            '产品类型': '电商',
            '台数': 2,
            '期数': 6,
            '每期还款金': 400,
            '合同金额': 2400,
            '第1期': '2024-02-25',
            '第2期': '2024-03-25',
            '第3期': '2024-04-25',
            '第4期': '2024-05-25',
            '第5期': '2024-06-25',
            '第6期': '2024-07-25',
            '备注': '电商测试订单'
        }
    ]
    
    # 资金流水账数据
    transaction_data = [
        # ORD001 新租赁4+2 交易
        {'日期': '2024-01-15', '订单编号': 'ORD001', '交易类型': '首付款', '交易金额': 600, '归属期数': '第1期', '备注': '首付款'},
        {'日期': '2024-02-15', '订单编号': 'ORD001', '交易类型': '租金', '交易金额': 800, '归属期数': '第1期', '备注': '第1期租金'},
        {'日期': '2024-03-15', '订单编号': 'ORD001', '交易类型': '租金', '交易金额': 800, '归属期数': '第2期', '备注': '第2期租金'},
        {'日期': '2024-04-15', '订单编号': 'ORD001', '交易类型': '租金', '交易金额': 800, '归属期数': '第3期', '备注': '第3期租金'},
        {'日期': '2024-05-15', '订单编号': 'ORD001', '交易类型': '租金', '交易金额': 800, '归属期数': '第4期', '备注': '第4期租金'},
        {'日期': '2024-06-15', '订单编号': 'ORD001', '交易类型': '尾款', '交易金额': 600, '归属期数': '第5期', '备注': '买断金第一期'},
        {'日期': '2024-07-15', '订单编号': 'ORD001', '交易类型': '尾款', '交易金额': 0, '归属期数': '第6期', '备注': '买断金第二期（已抵扣首付）'},
        
        # ORD002 旧租赁6期 交易
        {'日期': '2024-01-20', '订单编号': 'ORD002', '交易类型': '首付款', '交易金额': 600, '归属期数': '第1期', '备注': '首付款'},
        {'日期': '2024-02-20', '订单编号': 'ORD002', '交易类型': '租金', '交易金额': 600, '归属期数': '第1期', '备注': '第1期租金'},
        {'日期': '2024-03-20', '订单编号': 'ORD002', '交易类型': '租金', '交易金额': 600, '归属期数': '第2期', '备注': '第2期租金'},
        {'日期': '2024-04-20', '订单编号': 'ORD002', '交易类型': '租金', '交易金额': 600, '归属期数': '第3期', '备注': '第3期租金'},
        {'日期': '2024-05-20', '订单编号': 'ORD002', '交易类型': '租金', '交易金额': 600, '归属期数': '第4期', '备注': '第4期租金'},
        {'日期': '2024-06-20', '订单编号': 'ORD002', '交易类型': '租金', '交易金额': 600, '归属期数': '第5期', '备注': '第5期租金'},
        {'日期': '2024-07-20', '订单编号': 'ORD002', '交易类型': '租金', '交易金额': 300, '归属期数': '第6期', '备注': '第6期租金（已扣除买断金）'},
        
        # ORD003 电商 交易
        {'日期': '2024-02-25', '订单编号': 'ORD003', '交易类型': '租金', '交易金额': 400, '归属期数': '第1期', '备注': '第1期租金'},
        {'日期': '2024-03-25', '订单编号': 'ORD003', '交易类型': '租金', '交易金额': 400, '归属期数': '第2期', '备注': '第2期租金'},
        {'日期': '2024-04-25', '订单编号': 'ORD003', '交易类型': '租金', '交易金额': 400, '归属期数': '第3期', '备注': '第3期租金'},
        {'日期': '2024-05-25', '订单编号': 'ORD003', '交易类型': '租金', '交易金额': 400, '归属期数': '第4期', '备注': '第4期租金'},
        {'日期': '2024-06-25', '订单编号': 'ORD003', '交易类型': '租金', '交易金额': 400, '归属期数': '第5期', '备注': '第5期租金'},
        {'日期': '2024-07-25', '订单编号': 'ORD003', '交易类型': '租金', '交易金额': 400, '归属期数': '第6期', '备注': '第6期租金'},
    ]
    
    # @芳会资料补充数据
    customer_data = [
        {
            '订单编号': 'ORD001',
            '客户姓名': '张三',
            '手机号': '13800138001',
            '客服人员': '客服A',
            '业务归属': '北京业务部',
            '备注': '新租赁4+2客户'
        },
        {
            '订单编号': 'ORD002',
            '客户姓名': '李四',
            '手机号': '13800138002',
            '客服人员': '客服B',
            '业务归属': '上海业务部',
            '备注': '旧租赁6期客户'
        },
        {
            '订单编号': 'ORD003',
            '客户姓名': '王五',
            '手机号': '13800138003',
            '客服人员': '客服C',
            '业务归属': '广州业务部',
            '备注': '电商客户'
        }
    ]
    
    # 创建Excel文件
    with pd.ExcelWriter('sample_data/新租赁4+2测试数据.xlsx', engine='openpyxl') as writer:
        # 写入订单管理表
        pd.DataFrame(order_data).to_excel(writer, sheet_name='订单管理', index=False)
        
        # 写入资金流水账表
        pd.DataFrame(transaction_data).to_excel(writer, sheet_name='资金流水账', index=False)
        
        # 写入客户资料补充表
        pd.DataFrame(customer_data).to_excel(writer, sheet_name='@芳会资料补充', index=False)
    
    print("示例Excel文件已创建: sample_data/新租赁4+2测试数据.xlsx")
    
    # 创建简单的账单提取测试文件
    bill_test_data = [
        {'订单ID': 'ORD001', '客户姓名': '张三', '产品名称': 'iPhone15Pro256G'},
        {'订单ID': 'ORD002', '客户姓名': '李四', '产品名称': 'iPhone16Pro512G'},
        {'订单ID': 'ORD003', '客户姓名': '王五', '产品名称': '通用电商产品'}
    ]
    
    pd.DataFrame(bill_test_data).to_excel('sample_data/账单提取测试数据.xlsx', index=False)
    print("账单提取测试文件已创建: sample_data/账单提取测试数据.xlsx")

if __name__ == '__main__':
    create_sample_excel()
