#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库连接管理模块
提供PostgreSQL数据库连接和会话管理
"""

import logging
from contextlib import contextmanager
from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.pool import QueuePool
from .config import DatabaseConfig, CONNECTION_POOL_CONFIG

# 配置日志
logger = logging.getLogger(__name__)

# 创建基础模型类
Base = declarative_base()

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.engine = None
        self.SessionLocal = None
        self._initialized = False
    
    def initialize(self):
        """初始化数据库连接"""
        if self._initialized:
            return
        
        try:
            # 创建数据库引擎
            database_url = DatabaseConfig.get_database_url()
            self.engine = create_engine(
                database_url,
                poolclass=QueuePool,
                echo=DatabaseConfig.SQLALCHEMY_ECHO,
                **CONNECTION_POOL_CONFIG
            )
            
            # 添加连接事件监听器
            @event.listens_for(self.engine, "connect")
            def set_sqlite_pragma(dbapi_connection, connection_record):
                """设置连接参数"""
                pass
            
            # 创建会话工厂
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            self._initialized = True
            logger.info("数据库连接初始化成功")
            
        except Exception as e:
            logger.error(f"数据库连接初始化失败: {e}")
            raise
    
    def create_tables(self):
        """创建所有表"""
        if not self._initialized:
            self.initialize()
        
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("数据库表创建成功")
        except Exception as e:
            logger.error(f"数据库表创建失败: {e}")
            raise
    
    def drop_tables(self):
        """删除所有表"""
        if not self._initialized:
            self.initialize()
        
        try:
            Base.metadata.drop_all(bind=self.engine)
            logger.info("数据库表删除成功")
        except Exception as e:
            logger.error(f"数据库表删除失败: {e}")
            raise
    
    @contextmanager
    def get_session(self):
        """获取数据库会话上下文管理器"""
        if not self._initialized:
            self.initialize()
        
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"数据库会话错误: {e}")
            raise
        finally:
            session.close()
    
    def get_session_factory(self):
        """获取会话工厂"""
        if not self._initialized:
            self.initialize()
        return self.SessionLocal
    
    def close(self):
        """关闭数据库连接"""
        if self.engine:
            self.engine.dispose()
            logger.info("数据库连接已关闭")

# 全局数据库管理器实例
db_manager = DatabaseManager()

# 便捷函数
def get_db_session():
    """获取数据库会话"""
    return db_manager.get_session()

def init_database():
    """初始化数据库"""
    db_manager.initialize()

def create_tables():
    """创建数据库表"""
    db_manager.create_tables()

def get_engine():
    """获取数据库引擎"""
    if not db_manager._initialized:
        db_manager.initialize()
    return db_manager.engine
