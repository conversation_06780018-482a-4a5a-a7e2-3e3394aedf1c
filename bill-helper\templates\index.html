<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账单助手</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <style>
        body {
            font-family: "Microsoft YaHei", "Heiti SC", sans-serif;
            background-color: #f8f9fa;
            padding-top: 20px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f1f8ff;
            border-bottom: 1px solid #e1e4e8;
            font-weight: bold;
        }
        .btn-primary {
            background-color: #0d6efd;
        }
        .progress {
            height: 20px;
            margin-top: 10px;
        }
        .alert {
            margin-top: 15px;
        }
        #loading-spinner {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .step {
            flex: 1;
            text-align: center;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 5px;
            margin: 0 5px;
        }
        .step.active {
            background-color: #cfe2ff;
            font-weight: bold;
        }
        .step.completed {
            background-color: #d1e7dd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">账单助手系统</h1>
        <div class="row">
            <div class="col-md-10 offset-md-1">
                <!-- 步骤指示器 -->
                <div class="step-indicator">
                    <div class="step active" id="step1">1. 上传数据</div>
                    <div class="step" id="step2">2. 提取账单</div>
                    <div class="step" id="step3">3. 数据处理</div>
                    <div class="step" id="step4">4. 下载结果</div>
                </div>

                <!-- 错误消息显示区 -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category if category != 'message' else 'info' }}">
                                {{ message }}
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- 上传文件卡片 -->
                <div class="card">
                    <div class="card-header">上传订单数据文件</div>
                    <div class="card-body">
                        <form method="POST" action="/upload" enctype="multipart/form-data" id="upload-form">
                            <div class="mb-3">
                                <label for="file" class="form-label">选择Excel文件 (.xls, .xlsx)</label>
                                <input type="file" class="form-control" id="file" name="file" accept=".xls,.xlsx" required>
                                <div class="form-text">文件必须包含"订单ID"列</div>
                            </div>
                            <button type="submit" class="btn btn-primary" id="upload-btn">上传文件</button>
                        </form>
                    </div>
                </div>

                <!-- 账户信息卡片 -->
                <div class="card" id="account-card" style="display: none;">
                    <div class="card-header">平台账号信息</div>
                    <div class="card-body">
                        <form method="POST" action="/process" id="account-form">
                            <div class="mb-3">
                                <label for="system_type" class="form-label">选择系统</label>
                                <select class="form-select" id="system_type" name="system_type" required>
                                    <option value="old" selected>旧系统 (rent.admin.pgyh.cn)</option>
                                    <option value="new">新系统 (taitaixiangzu.admin.pgyh.cn)</option>
                                </select>
                                <div class="form-text">请选择要抓取数据的系统</div>
                            </div>
                            <div class="mb-3">
                                <label for="username" class="form-label">账号</label>
                                <input type="text" class="form-control" id="username" name="username" value="***********" required>
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">密码</label>
                                <input type="password" class="form-control" id="password" name="password" value="881017" required>
                            </div>
                            <input type="hidden" id="file_id" name="file_id">
                            <button type="submit" class="btn btn-primary" id="process-btn">开始处理</button>
                        </form>
                    </div>
                </div>

                <!-- 处理进度卡片 -->
                <div class="card" id="progress-card" style="display: none;">
                    <div class="card-header">处理进度</div>
                    <div class="card-body">
                        <div id="status-message">准备中...</div>
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" id="progress-bar">0%</div>
                        </div>
                        <div id="processing-details" class="mt-3"></div>
                    </div>
                </div>

                <!-- 结果卡片 -->
                <div class="card" id="result-card" style="display: none;">
                    <div class="card-header">处理结果</div>
                    <div class="card-body">
                        <div id="result-message"></div>
                        <div id="download-links" class="mt-3"></div>
                        <button class="btn btn-success mt-3" id="restart-btn">处理新文件</button>
                    </div>
                </div>

                <!-- 加载提示 -->
                <div id="loading-spinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p>处理中，请稍候...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script>
        $(document).ready(function() {
            // 上传表单提交
            $('#upload-form').on('submit', function(e) {
                e.preventDefault();
                
                // 显示加载状态
                $('#loading-spinner').show();
                $('#upload-btn').prop('disabled', true).text('上传中...');
                
                // 获取表单数据
                var formData = new FormData(this);
                
                // 发送AJAX请求
                $.ajax({
                    url: '/upload',
                    type: 'POST',
                    data: formData,
                    contentType: false,
                    processData: false,
                    success: function(response) {
                        $('#loading-spinner').hide();
                        $('#upload-btn').prop('disabled', false).text('上传文件');
                        
                        if (response.success) {
                            // 更新步骤指示器
                            $('#step1').removeClass('active').addClass('completed');
                            $('#step2').addClass('active');
                            
                            // 显示账户信息卡片
                            $('#account-card').show();
                            $('#file_id').val(response.file_id);
                            
                            // 滚动到账户卡片
                            $('html, body').animate({
                                scrollTop: $("#account-card").offset().top
                            }, 500);
                        } else {
                            alert('上传失败: ' + response.error);
                        }
                    },
                    error: function() {
                        $('#loading-spinner').hide();
                        $('#upload-btn').prop('disabled', false).text('上传文件');
                        alert('上传过程中发生错误，请重试');
                    }
                });
            });
            
            // 账户表单提交
            $('#account-form').on('submit', function(e) {
                e.preventDefault();
                
                // 显示加载状态和进度卡片
                $('#loading-spinner').show();
                $('#process-btn').prop('disabled', true).text('处理中...');
                $('#progress-card').show();
                $('#status-message').text('正在登录平台...');
                $('#progress-bar').css('width', '10%').attr('aria-valuenow', 10).text('10%');
                
                // 获取表单数据
                var formData = $(this).serialize();
                
                // 发送AJAX请求
                $.ajax({
                    url: '/process',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response.success) {
                            // 更新步骤指示器
                            $('#step2').removeClass('active').addClass('completed');
                            $('#step3').addClass('active');
                            
                            // 开始轮询处理状态
                            checkProcessStatus(response.task_id);
                        } else {
                            $('#loading-spinner').hide();
                            $('#process-btn').prop('disabled', false).text('开始处理');
                            $('#status-message').text('处理失败: ' + response.error);
                            alert('处理失败: ' + response.error);
                        }
                    },
                    error: function() {
                        $('#loading-spinner').hide();
                        $('#process-btn').prop('disabled', false).text('开始处理');
                        alert('处理过程中发生错误，请重试');
                    }
                });
            });
            
            // 检查处理状态
            function checkProcessStatus(taskId) {
                $.ajax({
                    url: '/status/' + taskId,
                    type: 'GET',
                    success: function(response) {
                        // 更新进度条和状态消息
                        $('#status-message').text(response.status_message);
                        $('#progress-bar').css('width', response.progress + '%').attr('aria-valuenow', response.progress).text(response.progress + '%');
                        
                        if (response.details) {
                            $('#processing-details').html(response.details);
                        }
                        
                        if (response.status === 'completed') {
                            // 处理完成
                            $('#loading-spinner').hide();
                            $('#process-btn').prop('disabled', false).text('开始处理');
                            
                            // 更新步骤指示器
                            $('#step3').removeClass('active').addClass('completed');
                            $('#step4').addClass('active');
                            
                            // 显示结果卡片
                            $('#result-card').show();
                            $('#result-message').html('处理完成！共处理 ' + response.order_count + ' 个订单，提取了 ' + response.bill_count + ' 条账单信息。');
                            
                            // 添加下载链接
                            var downloadHtml = '<p>请点击下面的链接下载处理结果：</p><ul>';
                            response.files.forEach(function(file) {
                                downloadHtml += '<li><a href="/download/' + file.id + '" class="btn btn-outline-primary btn-sm">' + file.name + '</a></li>';
                            });
                            downloadHtml += '</ul>';
                            $('#download-links').html(downloadHtml);
                            
                            // 滚动到结果卡片
                            $('html, body').animate({
                                scrollTop: $("#result-card").offset().top
                            }, 500);
                        } else if (response.status === 'failed') {
                            // 处理失败
                            $('#loading-spinner').hide();
                            $('#process-btn').prop('disabled', false).text('开始处理');
                            $('#status-message').text('处理失败: ' + response.error);
                            alert('处理失败: ' + response.error);
                        } else {
                            // 继续轮询
                            setTimeout(function() {
                                checkProcessStatus(taskId);
                            }, 2000);
                        }
                    },
                    error: function() {
                        $('#loading-spinner').hide();
                        $('#process-btn').prop('disabled', false).text('开始处理');
                        alert('无法获取处理状态，请重试');
                    }
                });
            }
            
            // 重新开始按钮
            $('#restart-btn').on('click', function() {
                // 重置表单和UI状态
                $('#upload-form')[0].reset();
                $('#account-form')[0].reset();
                $('#account-card').hide();
                $('#progress-card').hide();
                $('#result-card').hide();
                $('#processing-details').empty();
                $('#download-links').empty();
                
                // 重置步骤指示器
                $('.step').removeClass('active completed');
                $('#step1').addClass('active');
                
                // 滚动到顶部
                $('html, body').animate({
                    scrollTop: 0
                }, 500);
            });
        });
    </script>
</body>
</html>
