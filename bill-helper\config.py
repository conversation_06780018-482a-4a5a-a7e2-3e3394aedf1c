#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
配置文件
包含应用的全局配置信息
"""

import os

# 基础路径配置
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
UPLOAD_FOLDER = os.path.join(BASE_DIR, 'uploads')
PROCESSED_FOLDER = os.path.join(BASE_DIR, 'processed')

# 创建必要的目录
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(PROCESSED_FOLDER, exist_ok=True)

# 文件上传配置
ALLOWED_EXTENSIONS = {'xls', 'xlsx'}
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB

# 系统配置
SYSTEMS = {
    'old': {
        'name': '旧系统',
        'login_url': 'https://rent.admin.pgyh.cn/mamager/#/user/login',
        'order_details_url': 'https://rent.admin.pgyh.cn/mamager/#/Order/HomePage/Details?id={}',
        'home_url': 'https://rent.admin.pgyh.cn/mamager/#/Order/HomePage'
    },
    'new': {
        'name': '新系统', 
        'login_url': 'http://taitaixiangzu.admin.pgyh.cn/mamager/#/user/login',
        'order_details_url': 'http://taitaixiangzu.admin.pgyh.cn/mamager/#/Order/HomePage/Details?id={}',
        'home_url': 'http://taitaixiangzu.admin.pgyh.cn/mamager/#/Home'
    }
}

# 平台访问配置（保持向后兼容）
PLATFORM_URL = SYSTEMS['old']['login_url']
ORDER_DETAILS_URL = SYSTEMS['old']['order_details_url']

# 默认登录信息（仅用于开发测试，生产环境应通过界面输入）
DEFAULT_USERNAME = "18636818368"
DEFAULT_PASSWORD = "881017"
