@echo off
title 聚合二维码流水匹配器
echo.
echo ===============================================
echo        聚合二维码流水匹配器启动中...
echo ===============================================
echo.

cd /d "%~dp0"

echo 正在检查必要文件...
if not exist "TZ.xlsx" (
    echo 错误: 找不到 TZ.xlsx 文件
    echo 请确保 TZ.xlsx 文件在当前目录中
    pause
    exit /b 1
)

if not exist "LS.xlsx" (
    echo 错误: 找不到 LS.xlsx 文件
    echo 请确保 LS.xlsx 文件在当前目录中
    pause
    exit /b 1
)

echo 文件检查完成，正在启动匹配器...
echo.

python qr_flow_matcher.py

echo.
echo 匹配器已退出
pause 