#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库模型定义
定义订单、还款计划、交易、客户信息等表的SQLAlchemy模型
"""

from datetime import datetime
from sqlalchemy import Column, Integer, String, Float, Date, DateTime, Text, Boolean, ForeignKey, Index
from sqlalchemy.orm import relationship
from .connection import Base

class Order(Base):
    """订单表"""
    __tablename__ = 'orders'
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键，自增')
    order_date = Column(Date, nullable=False, comment='订单日期')
    order_number = Column(String(50), unique=True, nullable=False, index=True, comment='订单编号，唯一索引')
    customer_name = Column(String(100), nullable=False, index=True, comment='客户姓名，索引')
    model = Column(String(100), comment='型号')
    customer_attribute = Column(String(50), comment='客户属性')
    usage = Column(String(100), comment='用途')
    payment_cycle = Column(String(50), comment='还款周期')
    product_type = Column(String(50), nullable=False, index=True, comment='产品类型，索引')
    periods = Column(Integer, nullable=False, comment='期数')
    business = Column(String(100), comment='业务')
    total_receivable = Column(Float, comment='总待收')
    current_receivable = Column(Float, comment='当前待收')
    remarks = Column(Text, comment='备注')
    monthly_payment = Column(Float, comment='每期还款金')
    cost = Column(Float, comment='成本，自动计算')
    shop_affiliation = Column(String(100), comment='店铺归属')
    devices = Column(Integer, default=1, comment='台数')
    contract_amount = Column(Float, comment='合同金额/总待收')
    
    # 新增字段支持新租赁4+2
    scheme_version = Column(String(20), default='old', comment='租赁方案版本：old(旧租赁6期)/new(新租赁4+2)')
    billing_months = Column(Integer, default=6, comment='账期：自然月跨度')
    downpayment = Column(Float, comment='首付款')
    buyout_total = Column(Float, comment='买断金总额')
    
    created_at = Column(DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 关系
    payment_schedules = relationship("PaymentSchedule", back_populates="order", cascade="all, delete-orphan")
    transactions = relationship("Transaction", back_populates="order", cascade="all, delete-orphan")
    customer_info = relationship("CustomerInfo", back_populates="order", uselist=False, cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Order(order_number='{self.order_number}', customer_name='{self.customer_name}')>"

class PaymentSchedule(Base):
    """还款计划表"""
    __tablename__ = 'payment_schedules'
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键，自增')
    order_id = Column(Integer, ForeignKey('orders.id'), nullable=False, comment='订单ID，外键')
    period_number = Column(Integer, nullable=False, comment='期数')
    due_date = Column(Date, nullable=False, comment='应还日期')
    amount = Column(Float, nullable=False, comment='应还金额')
    paid_amount = Column(Float, default=0.0, comment='已还金额，自动统计')
    status = Column(String(20), default='未到期', comment='还款状态')
    
    # 新增字段支持Delta计算
    delta_amount = Column(Float, default=0.0, comment='差额：应还-实还')
    paid_amount_cached = Column(Float, comment='已还金额缓存，便于查询')
    delta_updated_at = Column(DateTime, comment='差额更新时间')
    
    # 新增字段支持导入报告
    auto_inferred = Column(Boolean, default=False, comment='是否自动推导的日期/金额')
    hint_from_textual_period = Column(Boolean, default=False, comment='是否采纳文字类期数提示')
    
    created_at = Column(DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 关系
    order = relationship("Order", back_populates="payment_schedules")
    
    # 索引
    __table_args__ = (
        Index('ix_payment_schedules_order_period', 'order_id', 'period_number'),
        Index('ix_payment_schedules_status_due_date', 'status', 'due_date'),
        Index('ix_payment_schedules_delta', 'delta_amount'),
    )
    
    def __repr__(self):
        return f"<PaymentSchedule(order_id={self.order_id}, period={self.period_number}, amount={self.amount})>"

class Transaction(Base):
    """交易表"""
    __tablename__ = 'transactions'
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键，自增')
    order_id = Column(Integer, ForeignKey('orders.id'), nullable=False, comment='订单ID，外键')
    transaction_date = Column(Date, nullable=False, comment='交易日期')
    order_number = Column(String(50), comment='订单编号')
    transaction_type = Column(String(50), nullable=False, comment='交易类型')
    amount = Column(Float, nullable=False, comment='交易金额')
    period_number = Column(String(20), comment='归属期数')
    product_type = Column(String(50), comment='产品类型')
    direction = Column(String(10), default='收入', comment='收入/支出')
    transaction_order_number = Column(String(100), comment='交易流水号')
    fund_flow = Column(String(100), comment='资金流向')
    available_balance = Column(Float, comment='可用余额')
    pending_withdrawal = Column(Float, comment='待出金')
    remarks = Column(Text, comment='备注')
    
    created_at = Column(DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    # 关系
    order = relationship("Order", back_populates="transactions")
    
    # 索引
    __table_args__ = (
        Index('ix_transactions_order_date', 'order_id', 'transaction_date'),
        Index('ix_transactions_type_period', 'transaction_type', 'period_number'),
        Index('ix_transactions_order_number', 'order_number'),
    )
    
    def __repr__(self):
        return f"<Transaction(order_id={self.order_id}, type='{self.transaction_type}', amount={self.amount})>"

class CustomerInfo(Base):
    """客户信息表"""
    __tablename__ = 'customer_info'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键，自增')
    order_id = Column(Integer, ForeignKey('orders.id'), unique=True, nullable=False, comment='订单ID，唯一外键')
    order_number = Column(String(50), unique=True, nullable=False, index=True, comment='订单编号，唯一索引')
    customer_name = Column(String(100), nullable=False, comment='客户姓名')
    phone = Column(String(20), comment='手机号码')
    rental_period = Column(String(50), comment='租期')
    customer_service = Column(String(50), comment='客服归属')
    business_affiliation = Column(String(50), comment='业务归属')
    remarks = Column(Text, comment='备注')

    created_at = Column(DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')

    # 关系
    order = relationship("Order", back_populates="customer_info")

    def __repr__(self):
        return f"<CustomerInfo(order_number='{self.order_number}', customer_name='{self.customer_name}')>"

class ImportReport(Base):
    """导入报告表"""
    __tablename__ = 'import_reports'

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键，自增')
    run_id = Column(String(50), unique=True, nullable=False, index=True, comment='运行ID，唯一标识')
    started_at = Column(DateTime, nullable=False, comment='开始时间')
    finished_at = Column(DateTime, comment='结束时间')
    status = Column(String(20), default='running', comment='状态：running/success/failed')

    # 统计信息
    total_orders = Column(Integer, default=0, comment='总订单数')
    total_transactions = Column(Integer, default=0, comment='总交易数')
    total_customers = Column(Integer, default=0, comment='总客户数')
    new_lease_orders = Column(Integer, default=0, comment='新租赁订单数')
    old_lease_orders = Column(Integer, default=0, comment='旧租赁订单数')
    ecommerce_orders = Column(Integer, default=0, comment='电商订单数')
    used_pricing_count = Column(Integer, default=0, comment='使用定价配置次数')
    used_reverse_calc_count = Column(Integer, default=0, comment='使用反推计算次数')
    contract_check_pass = Column(Integer, default=0, comment='合同校验通过数')
    contract_check_fail = Column(Integer, default=0, comment='合同校验失败数')

    # 报告内容（JSON格式）
    report_data = Column(Text, comment='完整报告数据（JSON）')
    error_message = Column(Text, comment='错误信息')

    created_at = Column(DateTime, default=datetime.utcnow, comment='创建时间')

    def __repr__(self):
        return f"<ImportReport(run_id='{self.run_id}', status='{self.status}')>"
