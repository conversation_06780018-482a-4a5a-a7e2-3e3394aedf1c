#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
登录认证模块
负责自动填充账号密码并智能检测登录成功
"""

import time
from playwright.sync_api import sync_playwright

def smart_login(page, username, password, timeout=60):
    """
    自动填充账号密码并智能检测登录成功
    
    参数:
        page: Playwright页面对象
        username: 登录用户名
        password: 登录密码
        timeout: 超时时间（秒）
    
    返回:
        是否登录成功
    """
    print("等待登录页面加载完成...")
    # 等待页面加载完成
    time.sleep(3)
    
    try:
        print("正在自动填充账号密码...")
        
        # 填充邮箱/手机号
        page.fill('#normal_login_email', username)
        print("成功填充账号")
        
        # 填充密码
        page.fill('#normal_login_password', password)
        print("成功填充密码")
        
        # 记录当前URL，用于后续比较
        initial_url = page.url
        print(f"登录前URL: {initial_url}")
        
        # 点击登录按钮
        login_button = page.locator('button:has-text("登录")')
        if login_button.count() > 0:
            login_button.click()
            print("成功点击登录按钮")
        else:
            print("未找到登录按钮")
            return False
        
        # 智能检测登录状态
        print("正在智能检测登录状态...")
        start_time = time.time()
        login_success = False
        
        while time.time() - start_time < timeout:
            # 方法1: 检查URL变化
            current_url = page.url
            if current_url != initial_url and "/login" not in current_url:
                print(f"URL已变化: {current_url}，判断为登录成功")
                login_success = True
                break
            
            # 方法2: 检查页面上是否存在登录后才有的元素
            try:
                # 检查顶部导航栏或用户信息元素
                header_elements = page.locator('.ant-layout-header')
                if header_elements.count() > 0:
                    print("检测到页面头部导航栏，判断为登录成功")
                    login_success = True
                    break
                
                # 检查侧边菜单
                menu_elements = page.locator('.ant-menu')
                if menu_elements.count() > 0:
                    print("检测到侧边菜单，判断为登录成功")
                    login_success = True
                    break
                
                # 检查是否有验证码输入框，如果有则提示手动登录
                code_input = page.locator('#normal_login_code')
                if code_input.count() > 0 and code_input.is_visible():
                    print("检测到需要输入验证码，请在浏览器中手动完成登录...")
                    # 等待用户手动输入验证码并登录
                    time.sleep(2)
                    continue
                
                # 检查是否有错误提示
                error_message = page.locator('.ant-form-explain')
                if error_message.count() > 0 and error_message.is_visible():
                    error_text = error_message.text_content()
                    print(f"登录出错: {error_text}")
                    return False
            except Exception as e:
                print(f"检测登录状态时出错: {e}")
            
            # 等待一段时间再检查
            time.sleep(1)
            
            # 方法3: 尝试访问一个需要登录才能访问的页面
            if time.time() - start_time > 10 and not login_success:  # 10秒后尝试
                try:
                    # 尝试访问订单页面
                    test_url = "https://rent.admin.pgyh.cn/mamager/#/Order/HomePage"
                    page.goto(test_url)
                    time.sleep(2)
                    
                    # 如果能成功访问且URL没有重定向回登录页，则认为登录成功
                    if "/login" not in page.url:
                        print(f"成功访问订单页面，判断为登录成功")
                        login_success = True
                        break
                    else:
                        # 如果被重定向回登录页，返回原来的页面继续尝试
                        page.goto(initial_url)
                except Exception as e:
                    print(f"尝试访问测试页面时出错: {e}")
                    page.goto(initial_url)  # 返回原来的页面
        
        if login_success:
            print("登录成功！")
            return True
        else:
            print(f"登录超时（{timeout}秒），未能检测到登录成功")
            return False
    
    except Exception as e:
        print(f"登录过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def init_browser():
    """
    初始化浏览器并返回页面对象
    
    返回:
        playwright: Playwright对象
        browser: 浏览器对象
        page: 页面对象
    """
    playwright = sync_playwright().start()
    
    try:
        # 首先尝试使用Chromium
        print("正在启动Chromium浏览器...")
        browser = playwright.chromium.launch(
            headless=False,
            args=[
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-field-trial-config',
                '--disable-back-forward-cache',
                '--disable-features=TranslateUI',
                '--disable-ipc-flooding-protection',
                '--no-first-run',
                '--no-default-browser-check'
            ]
        )
        
        context = browser.new_context(
            viewport={'width': 1280, 'height': 800},
            user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        page = context.new_page()
        print("Chromium浏览器启动成功")
        return playwright, browser, page
        
    except Exception as e:
        print(f"Chromium启动失败: {e}")
        print("正在尝试使用Firefox浏览器...")
        
        try:
            # 如果Chromium失败，尝试使用Firefox
            browser = playwright.firefox.launch(headless=False)
            context = browser.new_context(
                viewport={'width': 1280, 'height': 800}
            )
            page = context.new_page()
            print("Firefox浏览器启动成功")
            return playwright, browser, page
            
        except Exception as firefox_error:
            print(f"Firefox启动也失败: {firefox_error}")
            print("正在尝试使用Webkit浏览器...")
            
            try:
                # 最后尝试Webkit
                browser = playwright.webkit.launch(headless=False)
                context = browser.new_context(
                    viewport={'width': 1280, 'height': 800}
                )
                page = context.new_page()
                print("Webkit浏览器启动成功")
                return playwright, browser, page
                
            except Exception as webkit_error:
                print(f"所有浏览器都启动失败: {webkit_error}")
                playwright.stop()
                raise Exception("无法启动任何浏览器")

def login_platform(username, password, url=None):
    """
    登录平台并返回会话对象
    
    参数:
    - username: 用户名
    - password: 密码
    - url: 登录页面URL（可选）
    
    返回:
    - session: 包含登录状态的对象 (playwright, browser, page)
    - error: 错误信息（如有）
    """
    try:
        playwright, browser, page = init_browser()
        
        # 导航到登录页面
        login_url = url or "https://rent.admin.pgyh.cn/mamager/#/user/login"
        print(f"正在打开登录页面: {login_url}...")
        page.goto(login_url)
        
        # 自动填充账号密码并检测登录成功
        if not smart_login(page, username, password):
            browser.close()
            playwright.stop()
            return None, "登录失败，请检查账号密码是否正确"
        
        return (playwright, browser, page), None
    except Exception as e:
        try:
            browser.close()
            playwright.stop()
        except:
            pass
        return None, f"登录过程发生错误: {str(e)}"

def close_session(session):
    """
    关闭会话对象
    
    参数:
    - session: 由login_platform返回的会话对象
    """
    if session:
        try:
            playwright, browser, page = session
            browser.close()
            playwright.stop()
        except Exception as e:
            print(f"关闭会话时出错: {e}")
