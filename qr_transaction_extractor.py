#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
from typing import Tuple, List, Dict


class QRTransactionExtractor:
    """聚合二维码交易提取器"""
    
    def __init__(self, tz_file: str):
        """
        初始化提取器
        
        Args:
            tz_file: TZ.xlsx文件路径
        """
        self.tz_file = tz_file
        self.original_df = None
        self.qr_df = None
        
    def load_and_extract(self) -> Tuple[pd.DataFrame, List[int]]:
        """
        加载TZ.xlsx并提取聚合二维码交易
        
        Returns:
            (聚合二维码数据DataFrame, 原始索引列表)
        """
        try:
            print("📂 正在加载TZ.xlsx...")
            self.original_df = pd.read_excel(self.tz_file, sheet_name='Sheet1')
            print(f"   TZ表格加载成功，共 {len(self.original_df)} 条记录")
            
            # 提取聚合二维码交易
            print("🔍 正在筛选聚合二维码交易...")
            qr_mask = self.original_df['交易类型'] == '聚合二维码'
            self.qr_df = self.original_df[qr_mask].copy()
            
            if self.qr_df.empty:
                print("❌ 未找到聚合二维码交易记录")
                return pd.DataFrame(), []
            
            print(f"   找到 {len(self.qr_df)} 条聚合二维码交易记录")
            
            # 保存原始索引用于追踪
            original_indices = self.qr_df.index.tolist()
            self.qr_df['_original_index'] = original_indices
            
            # 转换为标准格式
            standard_df = self._convert_to_standard_format()
            
            return standard_df, original_indices
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return pd.DataFrame(), []
    
    def _convert_to_standard_format(self) -> pd.DataFrame:
        """转换为标准的匹配格式"""
        print("🔧 正在转换数据格式...")
        
        standard_data = []
        
        for _, row in self.qr_df.iterrows():
            # 处理时间格式
            payment_time = self._process_payment_time(row['日期'], row['时间'])
            
            # 转换数据
            record = {
                '姓名': row['客户姓名'],
                '金额': float(row['交易金额']) if pd.notna(row['交易金额']) else 0.0,
                '支付时间': payment_time,
                '流水号': '',  # 待匹配填入
                '备注': str(row['备注']) if pd.notna(row['备注']) else '',
                '_original_index': row['_original_index']  # 保持原始索引追踪
            }
            standard_data.append(record)
        
        standard_df = pd.DataFrame(standard_data)
        print(f"   数据格式转换完成，共 {len(standard_df)} 条记录")
        
        return standard_df
    
    def _process_payment_time(self, date_col, time_col) -> str:
        """
        处理支付时间，支持混合格式
        
        Args:
            date_col: 日期列（如：2025-06-08）
            time_col: 时间列（如：09:50:00 或 空值）
            
        Returns:
            完整的支付时间字符串
        """
        if pd.isna(date_col):
            return ''
        
        date_str = str(date_col)
        
        # 如果date_str已经包含时间信息
        if ' ' in date_str and len(date_str) > 10:
            return date_str
        
        # 只有日期，处理时间部分
        if pd.notna(time_col) and str(time_col).strip():
            time_str = str(time_col).strip()
            return f"{date_str} {time_str}"
        else:
            # 只有日期时，设置为当天23:59:59作为匹配上限
            return f"{date_str} 23:59:59"
    
    def get_statistics(self) -> Dict:
        """获取提取统计信息"""
        if self.original_df is None or self.qr_df is None:
            return {}
        
        total_records = len(self.original_df)
        qr_records = len(self.qr_df)
        
        # 统计各交易类型
        transaction_types = self.original_df['交易类型'].value_counts().to_dict()
        
        # 统计聚合二维码交易的金额分布
        qr_amounts = self.qr_df['交易金额'].dropna()
        amount_stats = {
            'min': float(qr_amounts.min()) if len(qr_amounts) > 0 else 0,
            'max': float(qr_amounts.max()) if len(qr_amounts) > 0 else 0,
            'mean': (float(qr_amounts.mean()) 
                    if len(qr_amounts) > 0 else 0),
            'total': float(qr_amounts.sum()) if len(qr_amounts) > 0 else 0
        }
        
        return {
            'total_records': total_records,
            'qr_records': qr_records,
            'qr_percentage': f"{qr_records/total_records*100:.1f}%" if total_records > 0 else "0%",
            'transaction_types': transaction_types,
            'qr_amount_stats': amount_stats
        }
    
    def save_extracted_data(self, qr_data: pd.DataFrame, output_file: str = None):
        """保存提取的聚合二维码数据"""
        if output_file is None:
            output_file = "聚合二维码交易记录_提取.xlsx"
        
        try:
            print(f"💾 正在保存提取数据到 {output_file}...")
            
            # 移除内部追踪列
            save_data = qr_data.drop(['_original_index'], axis=1, errors='ignore')
            
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                save_data.to_excel(writer, sheet_name='聚合二维码交易', index=False)
                
                # 添加统计信息
                stats = self.get_statistics()
                if stats:
                    stats_data = [
                        ['总记录数', stats['total_records']],
                        ['聚合二维码记录数', stats['qr_records']],
                        ['占比', stats['qr_percentage']],
                        ['', ''],
                        ['金额统计', ''],
                        ['最小金额', stats['qr_amount_stats']['min']],
                        ['最大金额', stats['qr_amount_stats']['max']],
                        ['平均金额', round(stats['qr_amount_stats']['mean'], 2)],
                        ['总金额', stats['qr_amount_stats']['total']]
                    ]
                    
                    stats_df = pd.DataFrame(stats_data, columns=['统计项', '数值'])
                    stats_df.to_excel(writer, sheet_name='提取统计', index=False)
                
                # 自动调整列宽
                for sheet_name in writer.sheets:
                    worksheet = writer.sheets[sheet_name]
                    for column in worksheet.columns:
                        max_length = 0
                        column_letter = column[0].column_letter
                        for cell in column:
                            try:
                                cell_length = len(str(cell.value))
                                if cell_length > max_length:
                                    max_length = cell_length
                            except Exception:
                                pass
                        adjusted_width = min(max_length + 2, 50)
                        worksheet.column_dimensions[column_letter].width = adjusted_width
            
            print(f"✅ 提取数据已保存到 {output_file}")
            
        except Exception as e:
            print(f"❌ 保存提取数据失败: {e}")


def main():
    """测试函数"""
    print("🚀 聚合二维码交易提取器测试")
    print("=" * 50)
    
    extractor = QRTransactionExtractor("TZ.xlsx")
    qr_data, original_indices = extractor.load_and_extract()
    
    if not qr_data.empty:
        print(f"\n📊 提取统计:")
        stats = extractor.get_statistics()
        for key, value in stats.items():
            if key != 'transaction_types' and key != 'qr_amount_stats':
                print(f"   {key}: {value}")
        
        print(f"\n👀 前3条记录预览:")
        preview_data = qr_data.head(3)[['姓名', '金额', '支付时间', '备注']]
        print(preview_data.to_string(index=False))
        
        # 保存提取结果
        extractor.save_extracted_data(qr_data)
    
    print("\n🎉 提取器测试完成！")


if __name__ == "__main__":
    main() 