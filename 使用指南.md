# Bill-Helper 项目使用指南

## 项目概述

本项目包含三个主要功能模块，每个模块都有独立的入口程序：

1. **账单助手系统** (`app.py`) - Web界面的账单到期时间提取工具
2. **聚合二维码流水匹配器** (`qr_flow_matcher.py`) - 智能匹配聚合二维码交易与银行流水
3. **多文件内容填充工具** (`multi_file_filler_correct.py`) - 批量处理Excel文件数据填充

---

## 功能一：账单助手系统 📋

### 入口文件
```bash
python bill-helper/app.py
```

### 功能描述
- **目标**：自动提取账单到期时间并与订单数据合并
- **界面**：Web界面（浏览器操作）
- **输入**：包含"订单ID"列的Excel文件
- **输出**：处理后的订单数据（包含账单到期时间）

### 使用步骤

1. **启动服务**
   ```bash
   cd bill-helper
   python app.py
   ```
   
2. **访问界面**
   - 打开浏览器访问：`http://localhost:5001`
   
3. **上传文件**
   - 点击"选择文件"按钮
   - 选择包含"订单ID"列的Excel文件（.xls或.xlsx）
   - 系统会自动验证文件格式
   
4. **配置参数**
   - 输入系统用户名和密码
   - 选择系统类型（新系统/旧系统）
   
5. **执行处理**
   - 点击"开始处理"按钮
   - 系统会显示实时进度
   - 处理完成后可下载结果文件

### 输出文件
- `处理后的订单数据_时间戳.xlsx` - 包含账单到期时间的完整订单数据

### 注意事项
- 确保Excel文件包含"订单ID"列
- 网络连接正常（需要访问外部系统）
- 系统账号有效且有权限访问订单详情

---

## 功能二：聚合二维码流水匹配器 🔍

### 入口文件
```bash
python qr_flow_matcher.py
```

### 功能描述
- **目标**：将TZ.xlsx中的聚合二维码交易与LS.xlsx中的银行流水进行智能匹配
- **界面**：命令行交互界面
- **输入**：TZ.xlsx（交易数据）+ LS.xlsx（银行流水）
- **输出**：多个匹配结果Excel文件

### 使用步骤

1. **准备文件**
   - 确保当前目录有 `TZ.xlsx`（包含聚合二维码交易）
   - 确保当前目录有 `LS.xlsx`（包含银行流水数据）

2. **运行程序**
   ```bash
   python qr_flow_matcher.py
   ```

3. **查看数据概览**
   - 程序会自动显示数据统计信息
   - 显示聚合二维码交易数量和占比

4. **确认执行**
   - 程序会询问是否开始匹配
   - 输入 `y` 或 `yes` 确认执行

5. **查看结果**
   - 程序会显示匹配成功率和质量分布
   - 显示匹配结果预览

### 输出文件
- `TZ_完整匹配结果.xlsx` - 完整的原始数据+匹配结果
- `聚合二维码匹配结果.xlsx` - 仅聚合二维码交易结果
- `综合匹配报告.xlsx` - 详细分析报告（含统计、已匹配、未匹配等）
- `聚合二维码交易记录_提取.xlsx` - 提取的原始聚合二维码数据

### 匹配策略
- **时间优先级**：40%（时间差越小越优先）
- **金额优先级**：25%（大金额优先）
- **客户唯一性**：20%（单一客户单日同金额优先）
- **订单完整性**：15%（有备注信息优先）

### 注意事项
- TZ.xlsx必须包含"交易类型"列，且有"聚合二维码"类型的记录
- LS.xlsx必须包含"交易时间"和"交易金额"列
- 程序会自动处理时间格式和金额格式

---

## 功能三：多文件内容填充工具 📊

### 入口文件
```bash
python multi_file_filler_correct.py
```

### 功能描述
- **目标**：批量处理多个Excel文件，将数据填充到目标文件TTXW.xlsx
- **界面**：命令行交互菜单
- **输入**：多个源Excel文件
- **输出**：更新后的TTXW.xlsx文件

### 使用步骤

1. **准备文件**
   - 确保有目标文件 `TTXW.xlsx`
   - 准备需要处理的源Excel文件

2. **运行程序**
   ```bash
   python multi_file_filler_correct.py
   ```

3. **选择源文件**
   - 选择菜单项 `1. 选择源文件`
   - 程序会自动扫描当前目录的Excel文件
   - 输入文件序号（用逗号分隔）或输入 `all` 选择全部

4. **预览数据**（可选）
   - 选择菜单项 `2. 预览数据`
   - 输入要预览的行数
   - 查看数据格式是否正确

5. **执行填充**
   - 选择菜单项 `3. 执行填充`
   - 确认操作（会清空目标文件现有数据）
   - 等待处理完成

### 菜单选项
1. **选择源文件** - 选择要处理的Excel文件
2. **预览数据** - 预览处理后的数据格式
3. **执行填充** - 执行数据填充操作
4. **查看已选择文件** - 显示当前选中的文件列表
5. **设置目标文件** - 更改目标文件名
6. **退出** - 退出程序

### 数据处理逻辑
- 自动处理10个还款期数据
- 按日期排序数据
- 同时填充"订单管理"和"资金流水账"两个工作表
- 生成放款记录和供应商利润记录

### 注意事项
- 操作前建议备份目标文件
- 程序会清空目标文件第5行及以后的所有数据
- 确保源文件格式符合预期

---

## 快速启动命令

### 方式一：直接运行
```bash
# 账单助手系统
python bill-helper/app.py

# 聚合二维码流水匹配器
python qr_flow_matcher.py

# 多文件内容填充工具
python multi_file_filler_correct.py
```

### 方式二：使用批处理文件
如果您经常使用，可以创建批处理文件：

**启动账单助手.bat**
```batch
@echo off
cd /d "%~dp0"
cd bill-helper
python app.py
pause
```

**启动流水匹配器.bat**
```batch
@echo off
cd /d "%~dp0"
python qr_flow_matcher.py
pause
```

**启动填充工具.bat**
```batch
@echo off
cd /d "%~dp0"
python multi_file_filler_correct.py
pause
```

---

## 常见问题解答

### Q1: 程序报错"模块未找到"
**A**: 确保安装了所需的Python包：
```bash
pip install -r requirements.txt
```

### Q2: 账单助手无法访问网页
**A**: 
- 检查是否有其他程序占用5001端口
- 确保防火墙允许Python访问网络
- 尝试访问 `http://127.0.0.1:5001`

### Q3: 流水匹配器找不到文件
**A**: 
- 确保TZ.xlsx和LS.xlsx在当前目录
- 检查文件名是否正确（区分大小写）
- 确保文件没有被其他程序占用

### Q4: 填充工具处理失败
**A**: 
- 检查目标文件TTXW.xlsx是否存在
- 确保Excel文件没有被打开
- 检查源文件格式是否正确

### Q5: 如何备份数据
**A**: 
- 账单助手：备份原始订单Excel文件
- 流水匹配器：备份TZ.xlsx和LS.xlsx
- 填充工具：备份TTXW.xlsx

---

## 技术支持

如果遇到问题，请检查：
1. Python版本（建议3.8+）
2. 依赖包是否完整安装
3. 文件权限是否正确
4. 文件格式是否符合要求

---

*最后更新：2024年* 