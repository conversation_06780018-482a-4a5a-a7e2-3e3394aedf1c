# 产品需求文档 - 新租赁（4+2）适配与导入规范

## 背景与目标
- 背景：历史业务以 Excel 为上游，系统已从 SQLite 迁移到 PostgreSQL。现产品新增“租赁 4+2（买断分两月，尾期抵扣首付）”方案。
- 目标：在不大改 Excel 结构的前提下，支持新租赁 4+2，统一导入规则，提升准确性与可观测性，保持对旧租赁/电商的兼容。

## 范围
- Excel 模板与填表规范（不改工作表名）
- ETL 导入与逐期应还生成（含 4+2 规则）
- 账单状态计算与导入报告（合同金额校验、估算来源、自动补期）
- 定价配置（pricing.csv）与机型规范化匹配

## 数据字典（Excel 三张表）

### 订单管理（必填为“是/否”）
- 订单编号（是）：字符串，唯一。
- 订单日期（是）：日期，YYYY-MM-DD。
- 客户姓名（是）：字符串。
- 店铺归属（是）：枚举（现有店铺名）。
- 产品类型（是）：枚举 [电商, 租赁]。
- 台数（是）：整数，默认 1。
- 期数（是）：整数；旧租赁=6，新租赁=4。
- 每期还款金（否）：数值；缺失时按 pricing 或合同金额反推。
- 合同金额/总待收（否）：数值；用于一致性校验与租金反推。
- 第1期…第6期（否）：日期；新租赁若仅到第4期，系统会自动补齐第5/6期。
- 备注（否）：文本。

注意：不强制新增“首付/买断金/账期”列，系统从“资金流水账”与 pricing 推导。

### 资金流水账
- 日期（是）：日期。
- 订单编号（否）：字符串（强烈建议填写）。
- 交易类型（是）：枚举 [首付款, 租金, 尾款, 放款, 复投, 供应商利润, 提成, 一次性支出, 固定支出, 风控充值, 服务器月租, 成本, 支出]。
- 交易金额（是）：数值（收入为正，支出为负或依现行约定）。
- 归属期数（否）：
  - 文字类：第X期/第一期/第二期…（表示当期完成/协商提示）；
  - 数字类：X 或 X期（表示当期部分/未完成）；
  - 新租赁买断金：两条“买一”和“买二”代表当期完成/协商提示，归属第5期与第6期，M1和M2代表第5/6期当期部分/未完成。
- 交易订单号/资金流向/可用余额/待出金/备注（否）：保留现有定义。

### @芳会资料补充
- 订单编号、客户姓名、手机号、客服人员、业务归属、备注（沿用现有）。

## 流程与职责（端到端）
1) 上传 Excel（或指定路径触发）→ 后台生成 run_id。
2) 读取配置（pricing.csv、同义词）→ 规范化与匹配。
3) 解析三张表 → 生成/补齐 PaymentSchedule（逐期应还）。
4) 聚合交易 → 逐期计算“实还/差额/状态”。
5) 合同金额一致性校验、估算来源记录、异常收集。
6) 写库（按当前全量策略/未来改为 UPSERT）与创建索引。
7) 生成导入报告（JSON）并在 API 响应中返回摘要。

## 配置文件规范

### 定价配置（config/pricing.csv）
- 字段：product_type, model_norm, model_aliases, periods, billing_months, scheme_version, rent_per_period, buyout_total, downpayment_default, currency, effective_from, effective_to, shop, remarks。
- 匹配优先级：精确 model_norm → 别名 → 同 product_type+periods 默认项。
- 生效：ETL 每次运行前读取，热加载，无需重启。

#### 配置加载位置与搜索顺序
- 标准路径：项目根目录 `config/pricing.csv`（推荐统一使用）。
- 兼容搜索顺序（代码已支持）：
  1) 当前工作目录下 `config/pricing.csv`；
  2) 与 `etl.py` 同级目录下的 `config/pricing.csv`；
  3) `etl.py` 上级目录下的 `config/pricing.csv`。
- 注意：为避免多路径导致的版本混淆，建议仅在标准路径维护并作为唯一真源。

### 型号同义词（可选，后续）
- 文件：config/model_synonyms.yaml
- 结构示例：
  - iphone16promax512g: ["iPhone16 Pro Max 512G", "iPhone 16 Pro Max 512G", "iPhone16ProMax512G"]
- 用于增强匹配成功率；与 lease_rules 的规范化共同作用。

## 导入报告规范（JSON Schema 草案）
- run_id: string
- started_at/finished_at: ISO8601
- summary:
  - total_orders/transactions/customers: number
  - new_lease_orders/old_lease_orders/ecommerce_orders: number
  - used_pricing_count/used_reverse_calc_count: number
  - contract_check_pass/fail: number
- issues: [
  - {order_number, type: [contract_mismatch|missing_p56|pricing_miss|period_conflict|partial_payment], severity: [info|warn|error], message}
]
- orders: [
  - {order_number, product_type, model_raw, model_norm, periods, devices, estimation: {rent|dp|buyout: [excel|pricing|reverse|null]},
     contract: {excel: number|null, computed: number|null, diff: number|null, pass: bool},
     schedules: [{period, due_date, expected_amount, paid_amount, status, hint_from_textual_period: bool, auto_inferred: bool}]}
]

API 响应：在 `/api/etl/trigger` 与 `/api/etl/upload` 的成功响应中附带 `report.summary` 与可选 `report_url` 或内联 `report`（受大小限制）。

### 报告 API 与下载接口规范（已实现）
- 获取报告
  - `GET /api/etl/report?run_id=<id>`：按 run_id 获取（需登录）。
  - `GET /api/etl/report?last=true`：获取最新报告（需登录）。
  - 响应：`{ success, run_id, report }`。
- 下载报告
  - `GET /api/etl/report/download?run_id=<id>&format=json`：下载 JSON 文件。
  - `GET /api/etl/report/download?run_id=<id>&format=csv`：下载按期明细 CSV（扁平化 orders.schedules）。
  - `GET /api/etl/report/download?run_id=<id>&format=csv&type=issues`：仅下载 issues 的 CSV。
  - 支持 `last=true` 替代 `run_id` 获取最新。
  - 返回 Content-Disposition 附件文件名：`import_report_<run_id>_(schedules|issues).csv`。

### 当期差额（Delta）可视化与存储
- 定义：`delta_amount = expected_amount - paid_amount`（按期），其中 `expected_amount`=PaymentSchedule.amount，`paid_amount`=当期“首付款/租金/尾款”聚合。
- 展示（无须改库）：导入报告与相关 API 在每期返回 `expected_amount`、`paid_amount`、`delta_amount`，用于页面与导出展示。
- 可选持久化（性能优化时考虑）：
  - 方案A：在 `payment_schedules` 增加 `delta_amount` 列并更新索引；
  - 方案B：创建只读视图 `v_payment_status` 计算并暴露 `delta_amount`；
  - 方案C：物化视图/汇总表，用于重型统计。
- 建议先采用“计算后返回”的方案，验证需求与查询压力后再决定是否持久化。

## 状态判定规则详情
- 实还：聚合该期“首付款/租金/尾款”（首付款仅计入第1期）。
- 应还：PaymentSchedule.amount（由规则生成）。
- 判定：
  - |应还-实还| ≤ 100 → 已还清（对比 due_date：提前/按时/逾期还款）。
  - |应还-实还| > 100 且归属期写法为文字类（如“第一期”）→ 协商结清。
  - 其他：部分还款或逾期未还/账单日/未到期（按日期与已还金额细分）。
- 阈值与策略可配置：容差（默认 100）、负的“第6期”抵扣归并到“第5期”的处理等。

## 逐期应还生成（算法回顾）
- 旧租赁（6）：1–5期=rent；6期=max(rent - buyout, 0)。
- 新租赁（4+2）：1–4期=rent；5期=buyout/2；6期=max(buyout/2 - downpayment, 0)，不足部分并入第5期（或记录告警）。
- 电商：各期=rent。
- 日期补齐：若仅到第4期，5/6 期=“第4期+1/+2月”。
- 数据来源：Excel > pricing.csv > 合同反推（均产生日志与报告标记）。

## 验收用例（最小集）
1) 新租赁 4+2（只填前4期日期，流水两笔尾款归属第5/第6期）
   - 期1–4 金额=rent；期5=buyout/2；期6=buyout/2 - 首付（<0置0，差额并入5）。
   - 自动补齐 5/6 期日期与日志提示；合同校验通过。
2) 旧租赁 6期（小额买断）
   - 期6=rent - buyout；其余=rent；状态正确。
3) 电商（统一每期）
   - 期1–6=rent；状态正确。
4) 文本提示冲突（写“第一期”但差额>100）
   - 以金额为准，报告标红提示复核。
5) 缺“每期还款金”，pricing 命中
   - 使用 pricing 值，报告标注估算来源。
6) 缺定价且有合同金额
   - 反推租金，报告标注来源与通过/不通过。

## 回归与兼容策略
- 不改旧表与历史填法，ETL 做强解析与兜底；
- 定价与同义词变更通过配置热加载；
- 报告结构向后兼容（新增字段不破坏旧消费者）。

## 发布与回滚计划（建议）
- 预发布：使用样例 Excel 回归 6 套用例；灰度导入非写库模式校验；
- 正式：开启写库并观察 ETL 日志与报告；
- 回滚：保留旧 ETL 脚本入口/开关，配置层面可快速关闭新规则。

## 风险清单与缓解
- 定价缺失或匹配失败 → 使用反推并高亮；完善配置与同义词。
- 填表不规范（日期/数值文本化） → 模板加数据验证、ETL 容错与告警。
- 并发调度重复执行 → 单实例调度与分布式锁（后续）。

## 术语约定
- 期数：租金分期数。旧租赁=6，新租赁=4。
- 账期：自然月跨度。新租赁账期=6（4+2）。
- 买断金：第5/第6个月两次“尾款”支付的总额，第二次需抵扣首付。

## Excel 结构与填表规范（最小改动）
- 工作表名保持不变：
  - 订单管理、资金流水账、@芳会资料补充

- 订单管理（保持现状，建议但不强制新增列）
  - 必填：订单编号、订单日期、客户姓名、店铺归属、产品类型（电商/租赁）、台数、期数、每期还款金（若有）。
  - 期次日期：建议提供“第1期…第6期”。若仅有前4期，新租赁将自动补齐第5/6期日期（见下）。
  - 可选：合同金额、租赁方案版本（旧租赁6期/新租赁4+2）。

- 资金流水账（关键）
  - 交易类型：首付款、租金、尾款（新租赁买断金使用“尾款”）。
  - 归属期数：允许两种写法并行，ETL 会强解析。
    - 文字类（如“第X期”“第一期”）：表示当期完成/协商完成的强提示；
    - 数字类（如“X”“X期”）：表示当期部分支付/仍未完成。
  - 新租赁买断金：必须两条“尾款”，归属“第5期”“第6期”。第6期金额可直接填“买断金/2-首付”的净额。

- @芳会资料补充（保持现状）
  - 订单编号、客户姓名、手机号、客服人员、业务归属、备注。

## 导入与计算规则（系统行为）
- 逐期应还生成（写入 PaymentSchedule.amount）
  - 旧租赁（期数=6）：
    - 1–5期=每期还款金；6期=每期还款金-买断金（不足0按0）。
  - 新租赁（期数=4，账期=6）：
    - 1–4期=每期还款金；5期=买断金/2；6期=买断金/2-首付（若<0，则6期=0，第5期=买断金-首付）。
    - 若第5/6期日期缺失：以“第4期日期+1/+2月”自动补齐（记录日志）。
  - 电商：各期=每期还款金。

- 数据来源优先级与估算
  1) 若“每期还款金”在订单管理中有值，优先使用。
  2) 若缺失，尝试从定价表 `config/pricing.csv` 匹配（按 产品类型/机型/期数，含同义词/别名）。
  3) 若仍缺并存在“合同金额+期数”，按“合同金额=首付+租金×期数+买断金”反推租金。
  4) 所有“估算来源”会在日志与导入报告中标注（rent/dp/buyout 来自 pricing 或反推）。

- 账单状态判定（与现逻辑一致，细化规则）
  - 实还：按期聚合“首付款/租金/尾款”（首付款仅计入第1期）。
  - 应还：使用上文逐期应还金额。
  - 判定：
    - |应还-实还| ≤ 100：视为已还清（提前/按时/逾期根据日期）；
    - > 100 且归属期为“文字类”提示（如“第一期”）：视为“协商结清”；
    - 其他：部分还款/逾期未还。

## 定价配置（pricing.csv）
- 位置：`config/pricing.csv`（已提供模板）。
- 关键字段：product_type、model_norm、model_aliases、periods、billing_months、scheme_version、rent_per_period、buyout_total、downpayment_default、有效期等。
- 匹配策略：规范化机型（去空白/大小写/同义词/容量后缀/常见罗马数字），优先精确匹配→别名→同 pt+periods 默认项。

## 合同金额一致性校验与导入报告
- 一致性校验：对存在“合同金额（或总待收）”的订单，计算值=首付+租金×期数+买断金；差额≤1元为通过，否则标红。
- 导入报告（新增）：每次导入产出结构化报告，字段包括：
  - 每订单每期：应还、实还、差额、状态、是否采纳“文字类”提示、是否协商结清；
  - 估算来源：rent/dp/buyout 来源（Excel/pricing/反推）；
  - 合同金额校验结果与差额；
  - 自动补齐的第5/第6期日期与金额；
  - 机型匹配命中（规范化/别名/默认）。
- 接口返回：`/api/etl/trigger` 与 `/api/etl/upload` 在成功响应中附带导入报告摘要与可选下载链接/对象。

## 兼容与回退
- 不强制改动旧单据与列名，ETL 做强解析与兜底，最大化兼容历史。
- 若缺买断金/首付/每期还款金：使用定价或反推估算，导入报告标记并提示复核。

## 验收标准
- 新租赁订单导入后：
  - PaymentSchedule 含1–6期，且第5/第6期金额符合分摊与抵扣规则；
  - 若未提供第5/第6期日期，系统自动补齐并在日志/报告中可追溯；
  - 合同金额一致性校验通过或清晰报告差异。
- 旧租赁/电商导入与状态不退化。
- 导入报告可在接口返回与前端页面查看（含“估算来源/校验/补期”）。

## 风险与约束
- 若“归属期数”填写与金额强冲突（如写“第一期”但差额过大），以金额为准并在报告标红提示人工复核。
- 定价配置未维护完全时，会触发估算与告警；需建立配置变更流程。

## 里程碑
1) 接入 4+2 规则、自动补期、pricing.csv、合同校验与估算来源日志（已完成）。
2) 导入报告 JSON 生成、持久化与 API/前端展示，下载 JSON/CSV（已完成）。
3) 阶段3收尾：基础指标 `/api/etl/metrics`（已完成），配置热加载流程说明（已补）。
4) 可选：同义词字典支持、物化与缓存（后续）。

## 测试步骤（验证脚本）
- 4+2 规则验证（逐期应还与日期推导）：
  - `python3 scripts/demo_validate_new_lease.py`
- 导入报告构建（内存DB）
  - `python3 scripts/demo_import_report.py`
- 报告存储读写
  - `PYTHONPATH=. python3 scripts/demo_report_store.py`
- 报告 API 获取
  - `PYTHONPATH=. python3 scripts/test_api_report_endpoint.py`
- 报告下载（JSON/CSV，含 issues-only）
  - `PYTHONPATH=. python3 scripts/test_api_report_download.py`
  - `PYTHONPATH=. python3 scripts/test_api_report_download_issues.py`

## 指标接口（/api/etl/metrics）
- 目标：提供导入后关键指标，便于运维观测。
- 路由：`GET /api/etl/metrics?delta_threshold=100&source=latest|db`
  - `delta_threshold`：Delta 异常阈值，默认 100。
  - `source`：`latest`（默认，从最新报告聚合）或 `db`（实时扫描数据库）。
- 响应示例：
```
{
  "success": true,
  "run_id": "20250101T000000Z",
  "generated_at": "2025-01-01T00:00:00Z",
  "delta_threshold": 100,
  "metrics": {
    "total_orders": 123,
    "total_schedules": 456,
    "contract_check_pass": 120,
    "contract_check_fail": 3,
    "delta_anomaly_schedules": 10,
    "orders_with_anomaly": 8,
    "overdue_orders": 5
  }
}
```

## 配置热加载流程说明（pricing 与同义词）
- pricing 热加载（已实现）：
  - 标准路径：项目根目录 `config/pricing.csv`；每次运行 ETL 前自动重新读取最新配置（无需重启进程）。
  - 兼容搜索：工作目录/config → `etl.py` 同级/config → 上级/config（建议统一放标准路径）。
  - 变更流程：
    1) 修改并保存 `config/pricing.csv`；
    2) 运行导入（上传或触发 ETL），本次导入将使用最新配置；
    3) 在导入报告中核对“估算来源/合同校验/期次应还变化”。
- 型号同义词（预留方案，未实现）：
  - 文件：`config/model_synonyms.yaml`（规范 → 别名列表）；
  - 使用：与机型规范化共同提高匹配率；
  - 计划：后续按需接入，支持热加载与测试脚本。

## 当期差额（Delta）落地（选择方案A：加列持久化）

### 定义与写入时机
- Delta 定义：`delta_amount = expected_amount - paid_amount`（按期）。
  - expected_amount：逐期应还（PaymentSchedule.amount）。
  - paid_amount：该期“首付款/租金/尾款”的聚合值（首付款仅计入第1期）。
- 写入时机：在状态计算过程中（`update_payment_status_and_receivable`）同步计算并写入 `payment_schedules.delta_amount`；导入完成后的一次性重算也会刷新该值。

### 数据库变更（DDL）
- 目标表：`payment_schedules`
- 新增列：
  - `delta_amount` DOUBLE PRECISION NOT NULL DEFAULT 0
  - 可选：`paid_amount_cached` DOUBLE PRECISION（便于查询，不影响真值以聚合为准）
  - 可选：`delta_updated_at` TIMESTAMP（记录刷新时间）
- 索引建议（按需）：
  - `(status, due_date)` 已存在或由现有索引覆盖；
  - 如需快速筛查异常：`CREATE INDEX ix_payment_schedules_delta ON payment_schedules (delta_amount);`
- 迁移策略：
  - 使用现有迁移脚本机制新增 DDL；
  - 部署后首次运行：执行一次“状态重算”以回填 delta；
  - 回滚：仅删除列即可（不影响业务表主流程）。

### API/报表对接
- 导入报告：每期返回 `expected_amount`、`paid_amount`、`delta_amount`；
- 查询接口（后续）：支持按店铺/日期筛选“delta 异常”订单（|delta|>阈值），用于运营核对。

### 可视化（前端）
- 在上传页面或报告页：
  - 订单 → 期次列表：应还、实还、delta、状态；
  - 高亮：`|delta| > 100` 或“文字提示与金额冲突”。

## 阶段性实施计划（建议）

### 阶段1：规则与配置（1–2 天）
- 完成：4+2/6期逐期应还、自动补 5/6 期、pricing.csv、机型规范化、合同金额校验、估算来源日志。
- 验收：用样例 Excel 跑通三类用例；日志包含“估算/合同校验/补期”。

### 阶段2：导入报告与 Delta 计算（2–4 天）
- 增加：导入报告 JSON（含每期 expected/paid/delta、状态、估算来源、补期、合同校验差异）。
- 新增列：`payment_schedules.delta_amount` 并在状态计算时写入；提供首次回填脚本/入口。
- API：`/api/etl/trigger`、`/api/etl/upload` 返回报告摘要与可选明细；SSE 日志已覆盖关键语句。
- 验收：报告字段齐全、格式符合文档；数据库 delta 列正确回填且与报告一致。

### 阶段3：页面与运维（2–3 天）
- 页面：上传完成后展示“导入摘要 + 期次明细 + delta 高亮”；支持下载报告 JSON/CSV。
- 运维：配置热加载说明文档；定价/同义词变更流程；监控指标（估算命中、合同校验失败、delta 异常占比）。
- 验收：手工改动 pricing 后无须重启即可生效；报告展示稳定；关键指标可观察。

### 阶段4：性能与后续（可选，1–2 周）
- 若统计查询较重：增加视图/物化视图、接口缓存、汇总表。
- 调度：单实例与防重；批处理窗口内重算与锁机制。
- 进一步统一鉴权与安全基线（不影响本次业务落地）。
