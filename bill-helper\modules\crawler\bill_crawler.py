#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
账单数据抓取模块
负责自动访问指定订单页面并提取账单到期时间信息
"""

import time
import pandas as pd
from datetime import datetime

def click_bill_info_tab(page, max_retries=3):
    """
    智能点击账单信息标签
    
    参数:
        page: Playwright页面对象
        max_retries: 最大重试次数
    
    返回:
        是否成功点击
    """
    print("准备点击账单信息标签")
    
    # 直接尝试使用文本选择器定位账单信息标签
    for retry in range(max_retries):
        try:
            # 方法1: 使用文本内容直接定位
            bill_tab = page.locator('div[role="tab"]:has-text("账单信息")')
            if bill_tab.count() > 0:
                bill_tab.click()
                print("成功点击账单信息标签（使用文本选择器）")
                return True
            
            # 方法2: 使用更精确的选择器
            bill_tab = page.locator('.ant-tabs-tab:has-text("账单信息")')
            if bill_tab.count() > 0:
                bill_tab.click()
                print("成功点击账单信息标签（使用类选择器）")
                return True
            
            # 方法3: 遍历所有标签
            print("尝试遍历标签查找账单信息...")
            tabs = page.locator(".ant-tabs-tab")
            count = tabs.count()
            
            if count > 0:
                print(f"找到 {count} 个标签")
                
                # 直接查找第8个标签（根据之前的输出，账单信息通常是第8个）
                if count >= 8:
                    tab_text = tabs.nth(7).text_content()  # 索引从0开始，所以第8个是7
                    print(f"第8个标签文本: {tab_text}")
                    if "账单信息" in tab_text:
                        tabs.nth(7).click()
                        print("成功点击第8个标签（账单信息）")
                        return True
                
                # 如果第8个不是，则遍历所有标签
                for i in range(count):
                    tab_text = tabs.nth(i).text_content()
                    if "账单信息" in tab_text:
                        print(f"找到账单信息标签，位置: {i+1}")
                        tabs.nth(i).click()
                        print("成功点击账单信息标签")
                        return True
            
            # 方法4: 使用JavaScript点击
            found = page.evaluate("""
                () => {
                    const tabs = document.querySelectorAll('.ant-tabs-tab');
                    for (let i = 0; i < tabs.length; i++) {
                        if (tabs[i].textContent.includes('账单信息')) {
                            tabs[i].click();
                            return true;
                        }
                    }
                    return false;
                }
            """)
            
            if found:
                print("成功使用JavaScript点击账单信息标签")
                return True
            
            print(f"未找到账单信息标签，等待页面加载... (尝试 {retry+1}/{max_retries})")
            time.sleep(2)
            
        except Exception as e:
            print(f"点击标签时出错: {e} (尝试 {retry+1}/{max_retries})")
            time.sleep(2)
    
    print("无法找到账单信息标签")
    return False

def extract_bill_dates(page, order_id, order_details_url_template=None):
    """
    提取指定订单的账单到期时间
    
    参数:
        page: Playwright页面对象
        order_id: 订单ID
        order_details_url_template: 订单详情URL模板
    
    返回:
        包含账单信息的列表
    """
    results = []
    
    # 访问订单详情页面
    if order_details_url_template:
        url = order_details_url_template.format(order_id)
    else:
        url = f"https://rent.admin.pgyh.cn/mamager/#/Order/HomePage/Details?id={order_id}"
    print(f"\n正在访问订单: {order_id}")
    # 先访问页面
    page.goto(url, wait_until="networkidle")
    # 然后使用reload()方法强制刷新当前页面
    page.reload(wait_until="networkidle")
    
    # 等待页面加载
    time.sleep(3)
    
    # 智能点击账单信息标签
    if not click_bill_info_tab(page):
        print(f"无法点击账单信息标签，跳过订单 {order_id}")
        return results
    
    # 等待账单信息加载
    time.sleep(2)
    
    # 提取账单信息文本
    try:
        # 使用JavaScript读取分期信息表格中的账单到期时间
        bill_dates = page.evaluate("""
            () => {
                // 尝试定位分期信息表格
                const result = [];
                
                // 定位分期信息的标题
                const titleElements = Array.from(document.querySelectorAll('.antd-pro-components-custom-card-index-title'));
                let tableElement = null;
                
                // 查找"分期信息"相关的表格
                for (const el of titleElements) {
                    if (el.textContent && el.textContent.includes('分期信息')) {
                        // 找到分期信息表格
                        const cardWrapper = el.closest('.antd-pro-components-custom-card-index-wrapper');
                        if (cardWrapper) {
                            // 找到包含表格的父元素
                            tableElement = cardWrapper.nextElementSibling;
                            break;
                        }
                    }
                }
                
                if (!tableElement) {
                    // 没找到表格，尝试另一种方法
                    const tables = document.querySelectorAll('.ant-table-tbody');
                    for (const table of tables) {
                        // 检查表格是否包含期数和到期时间
                        const headers = table.closest('.ant-table').querySelectorAll('th');
                        let hasInstalmentColumn = false;
                        let hasDueDateColumn = false;
                        let dueDateColumnIndex = -1;
                        
                        for (let i = 0; i < headers.length; i++) {
                            const headerText = headers[i].textContent;
                            if (headerText.includes('当前期数')) {
                                hasInstalmentColumn = true;
                            }
                            if (headerText.includes('账单到期时间')) {
                                hasDueDateColumn = true;
                                dueDateColumnIndex = i;
                            }
                        }
                        
                        if (hasInstalmentColumn && hasDueDateColumn) {
                            tableElement = table;
                            break;
                        }
                    }
                }
                
                if (tableElement) {
                    // 获取表格行
                    const rows = tableElement.querySelectorAll('tr');
                    
                    for (const row of rows) {
                        const cells = row.querySelectorAll('td');
                        if (cells.length >= 5) {
                            // 第一列是期数，第五列是账单到期时间
                            const instalment = cells[0].textContent.trim();
                            const dueDate = cells[4].textContent.trim();
                            
                            if (instalment && dueDate) {
                                result.push({
                                    instalment: instalment,
                                    dueDate: dueDate
                                });
                            }
                        }
                    }
                }
                
                return result;
            }
        """)
        
        if bill_dates and len(bill_dates) > 0:
            print("成功获取账单信息")
            
            # 处理提取的数据
            for item in bill_dates:
                instalment = item.get('instalment')
                due_date = item.get('dueDate', '')
                
                # 只保留年月日部分
                if due_date and len(due_date) >= 10:
                    date_only = due_date[:10]  # 提取前10个字符 (YYYY-MM-DD)
                else:
                    date_only = due_date
                
                # 添加到结果列表
                results.append({
                    "订单ID": order_id,
                    "期数": instalment,
                    "账单到期日期": date_only
                })
                print(f"期数 {instalment}: {due_date}")
        else:
            # 如果先前的方法失败，尝试直接定位表格的第五列数据
            try:
                # 直接获取表格中的账单到期时间列
                table_data = page.evaluate("""
                    () => {
                        const result = [];
                        // 获取所有表格
                        const tables = document.querySelectorAll('.ant-table-tbody');
                        
                        for (const table of tables) {
                            const rows = table.querySelectorAll('tr');
                            const headerRow = table.closest('.ant-table').querySelector('thead tr');
                            
                            if (headerRow) {
                                const headers = headerRow.querySelectorAll('th');
                                let dueDateColumnIndex = -1;
                                let periodColumnIndex = -1;
                                
                                // 查找账单到期时间列的索引
                                for (let i = 0; i < headers.length; i++) {
                                    const headerText = headers[i].textContent;
                                    if (headerText.includes('账单到期时间')) {
                                        dueDateColumnIndex = i;
                                    }
                                    if (headerText.includes('当前期数') || headerText.includes('期数')) {
                                        periodColumnIndex = i;
                                    }
                                }
                                
                                // 如果找到了账单到期时间列
                                if (dueDateColumnIndex >= 0 && periodColumnIndex >= 0) {
                                    for (const row of rows) {
                                        const cells = row.querySelectorAll('td');
                                        if (cells.length > dueDateColumnIndex && cells.length > periodColumnIndex) {
                                            const period = cells[periodColumnIndex].textContent.trim();
                                            const dueDate = cells[dueDateColumnIndex].textContent.trim();
                                            
                                            if (period && dueDate) {
                                                result.push({
                                                    instalment: period,
                                                    dueDate: dueDate
                                                });
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        return result;
                    }
                """)
                
                if table_data and len(table_data) > 0:
                    print("成功获取账单信息（备用方法）")
                    
                    # 处理提取的数据
                    for item in table_data:
                        instalment = item.get('instalment')
                        due_date = item.get('dueDate', '')
                        
                        # 只保留年月日部分
                        if due_date and len(due_date) >= 10:
                            date_only = due_date[:10]  # 提取前10个字符 (YYYY-MM-DD)
                        else:
                            date_only = due_date
                        
                        # 添加到结果列表
                        results.append({
                            "订单ID": order_id,
                            "期数": instalment,
                            "账单到期日期": date_only
                        })
                        print(f"期数 {instalment}: {due_date}")
                else:
                    print(f"无法获取账单信息，跳过订单 {order_id}")
            except Exception as e:
                print(f"备用方法提取账单信息时出错: {e}")
                print(f"无法获取账单信息，跳过订单 {order_id}")
    except Exception as e:
        print(f"提取账单信息时出错: {e}")
        # 发生错误时尝试备用方法
        try:
            # 使用备用方法直接定位表格元素
            print("尝试备用方法提取账单信息...")
            
            # 截图并保存，便于调试
            page.screenshot(path=f"debug_order_{order_id}.png")
            print(f"已保存页面截图到 debug_order_{order_id}.png")
            
            # 获取表格内容中的日期
            table_dates = page.evaluate("""
                () => {
                    // 获取所有包含日期格式的文本
                    const datePattern = /\d{4}-\d{2}-\d{2}/;
                    const elements = document.querySelectorAll('td');
                    const dates = [];
                    
                    for (let i = 0; i < elements.length; i++) {
                        const text = elements[i].textContent.trim();
                        if (datePattern.test(text)) {
                            // 如果是前一个元素是数字（可能是期数）
                            let period = '';
                            if (i > 0 && /^\\d+$/.test(elements[i-1].textContent.trim())) {
                                period = elements[i-1].textContent.trim();
                            } else if (i > 0) {
                                // 尝试查找同一行的第一个单元格
                                const row = elements[i].closest('tr');
                                if (row) {
                                    const firstCell = row.querySelector('td');
                                    if (firstCell && /^\\d+$/.test(firstCell.textContent.trim())) {
                                        period = firstCell.textContent.trim();
                                    }
                                }
                            }
                            
                            dates.push({
                                instalment: period || dates.length + 1,
                                dueDate: text
                            });
                        }
                    }
                    
                    return dates;
                }
            """)
            
            if table_dates and len(table_dates) > 0:
                print("使用备用方法成功获取账单日期")
                
                # 处理提取的数据
                for item in table_dates:
                    instalment = item.get('instalment')
                    due_date = item.get('dueDate', '')
                    
                    # 只保留年月日部分
                    if due_date and len(due_date) >= 10:
                        date_only = due_date[:10]  # 提取前10个字符 (YYYY-MM-DD)
                    else:
                        date_only = due_date
                    
                    # 添加到结果列表
                    results.append({
                        "订单ID": order_id,
                        "期数": instalment,
                        "账单到期日期": date_only
                    })
                    print(f"期数 {instalment}: {due_date}")
        except Exception as backup_error:
            print(f"备用方法也失败: {backup_error}")
    
    return results

def fetch_bill_due_dates(session, order_ids, order_details_url_template=None):
    """
    提取多个订单的账单到期时间
    
    参数:
    - session: 已登录的会话对象 (playwright, browser, page)
    - order_ids: 订单ID列表
    - order_details_url_template: 订单详情URL模板
    
    返回:
    - due_dates: 到期时间信息列表
    - error: 错误信息（如有）
    """
    try:
        _, _, page = session
        all_results = []
        
        # 遍历每个订单ID
        for order_id in order_ids:
            # 提取当前订单的账单到期时间
            order_results = extract_bill_dates(page, order_id, order_details_url_template)
            all_results.extend(order_results)
        
        return all_results, None
    except Exception as e:
        return None, f"提取账单到期时间时出错: {str(e)}"

def save_results_to_excel(results, output_file=None):
    """
    将提取的结果保存到Excel文件
    
    参数:
    - results: 提取的账单到期时间信息列表
    - output_file: 输出文件名（可选）
    
    返回:
    - output_path: 输出文件路径
    - error: 错误信息（如有）
    """
    try:
        if not results:
            return None, "没有数据可保存"
        
        # 创建DataFrame
        df = pd.DataFrame(results)
        
        # 生成输出文件名
        if not output_file:
            current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"订单账单到期时间_{current_time}.xlsx"
        
        # 保存结果到Excel文件
        df.to_excel(output_file, index=False)
        
        return output_file, None
    except Exception as e:
        return None, f"保存结果到Excel时出错: {str(e)}"
