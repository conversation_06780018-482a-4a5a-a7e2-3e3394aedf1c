#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库配置模块
管理PostgreSQL数据库连接配置
"""

import os
from urllib.parse import quote_plus

# 数据库配置
class DatabaseConfig:
    """数据库配置类"""
    
    # PostgreSQL 配置
    POSTGRES_HOST = os.getenv('POSTGRES_HOST', 'localhost')
    POSTGRES_PORT = os.getenv('POSTGRES_PORT', '5432')
    POSTGRES_DB = os.getenv('POSTGRES_DB', 'bill_helper')
    POSTGRES_USER = os.getenv('POSTGRES_USER', 'postgres')
    POSTGRES_PASSWORD = os.getenv('POSTGRES_PASSWORD', 'password')
    
    # SQLAlchemy 配置
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ECHO = os.getenv('SQLALCHEMY_ECHO', 'False').lower() == 'true'
    
    @classmethod
    def get_database_url(cls):
        """获取数据库连接URL"""
        password = quote_plus(cls.POSTGRES_PASSWORD)
        return f"postgresql://{cls.POSTGRES_USER}:{password}@{cls.POSTGRES_HOST}:{cls.POSTGRES_PORT}/{cls.POSTGRES_DB}"
    
    @classmethod
    def get_async_database_url(cls):
        """获取异步数据库连接URL"""
        password = quote_plus(cls.POSTGRES_PASSWORD)
        return f"postgresql+asyncpg://{cls.POSTGRES_USER}:{password}@{cls.POSTGRES_HOST}:{cls.POSTGRES_PORT}/{cls.POSTGRES_DB}"

# 连接池配置
CONNECTION_POOL_CONFIG = {
    'pool_size': 10,
    'max_overflow': 20,
    'pool_timeout': 30,
    'pool_recycle': 3600,
    'pool_pre_ping': True
}

# 数据库表前缀
TABLE_PREFIX = os.getenv('TABLE_PREFIX', '')

# 迁移配置
MIGRATION_DIR = os.path.join(os.path.dirname(__file__), 'migrations')
