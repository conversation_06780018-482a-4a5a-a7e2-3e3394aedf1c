#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
定价配置管理模块
负责加载和管理pricing.csv配置文件
"""

import os
import pandas as pd
import yaml
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import re

logger = logging.getLogger(__name__)

class PricingConfigManager:
    """定价配置管理器"""
    
    def __init__(self):
        self.pricing_data = None
        self.model_synonyms = {}
        self.config_loaded = False
    
    def load_pricing_config(self) -> bool:
        """
        加载定价配置文件
        
        Returns:
            是否加载成功
        """
        try:
            # 搜索配置文件路径
            config_path = self._find_pricing_config_path()
            if not config_path:
                logger.error("未找到pricing.csv配置文件")
                return False
            
            # 读取定价配置
            self.pricing_data = pd.read_csv(config_path)
            logger.info(f"成功加载定价配置，共 {len(self.pricing_data)} 条记录")
            
            # 加载型号同义词
            self._load_model_synonyms()
            
            self.config_loaded = True
            return True
            
        except Exception as e:
            logger.error(f"加载定价配置失败: {e}")
            return False
    
    def _find_pricing_config_path(self) -> Optional[str]:
        """
        查找定价配置文件路径
        按照文档中的搜索顺序查找
        
        Returns:
            配置文件路径，如果未找到则返回None
        """
        search_paths = [
            # 1. 项目根目录
            os.path.join(os.getcwd(), 'config', 'pricing.csv'),
            # 2. 当前工作目录
            os.path.join(os.getcwd(), 'config', 'pricing.csv'),
            # 3. ETL模块同级目录
            os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'pricing.csv'),
            # 4. ETL模块上级目录
            os.path.join(os.path.dirname(__file__), '..', 'config', 'pricing.csv')
        ]
        
        for path in search_paths:
            abs_path = os.path.abspath(path)
            if os.path.exists(abs_path):
                logger.info(f"找到定价配置文件: {abs_path}")
                return abs_path
        
        return None
    
    def _load_model_synonyms(self):
        """加载型号同义词配置"""
        try:
            # 查找同义词配置文件
            synonyms_paths = [
                os.path.join(os.getcwd(), 'config', 'model_synonyms.yaml'),
                os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'model_synonyms.yaml')
            ]
            
            synonyms_path = None
            for path in synonyms_paths:
                if os.path.exists(path):
                    synonyms_path = path
                    break
            
            if synonyms_path:
                with open(synonyms_path, 'r', encoding='utf-8') as f:
                    self.model_synonyms = yaml.safe_load(f) or {}
                logger.info(f"成功加载型号同义词配置，共 {len(self.model_synonyms)} 个型号")
            else:
                logger.warning("未找到型号同义词配置文件，将使用内置规范化规则")
                
        except Exception as e:
            logger.warning(f"加载型号同义词配置失败: {e}")
            self.model_synonyms = {}
    
    def normalize_model_name(self, model_name: str) -> str:
        """
        规范化型号名称
        
        Args:
            model_name: 原始型号名称
            
        Returns:
            规范化后的型号名称
        """
        if not model_name:
            return ""
        
        # 去除空白字符
        normalized = model_name.strip()
        
        # 转换为小写用于匹配
        lower_name = normalized.lower()
        
        # 检查同义词映射
        for standard_name, aliases in self.model_synonyms.items():
            if lower_name in [alias.lower() for alias in aliases]:
                return standard_name
        
        # 使用内置规范化规则
        normalized = self._apply_builtin_normalization(normalized)
        
        return normalized
    
    def _apply_builtin_normalization(self, model_name: str) -> str:
        """
        应用内置规范化规则
        
        Args:
            model_name: 型号名称
            
        Returns:
            规范化后的型号名称
        """
        # 去除空白字符
        normalized = re.sub(r'\s+', '', model_name)
        
        # 转换为小写
        normalized = normalized.lower()
        
        # 处理常见的容量后缀
        normalized = re.sub(r'gb$', 'g', normalized)
        
        # 处理罗马数字
        roman_map = {'ⅰ': '1', 'ⅱ': '2', 'ⅲ': '3', 'ⅳ': '4', 'ⅴ': '5'}
        for roman, digit in roman_map.items():
            normalized = normalized.replace(roman, digit)
        
        return normalized
    
    def find_pricing_config(self, product_type: str, model_name: str, periods: int) -> Optional[Dict[str, Any]]:
        """
        查找匹配的定价配置
        
        Args:
            product_type: 产品类型
            model_name: 型号名称
            periods: 期数
            
        Returns:
            匹配的配置记录，如果未找到则返回None
        """
        if not self.config_loaded or self.pricing_data is None:
            logger.warning("定价配置未加载")
            return None
        
        # 规范化型号名称
        normalized_model = self.normalize_model_name(model_name)
        
        # 过滤有效配置（检查有效期）
        current_date = datetime.now().date()
        valid_configs = self.pricing_data[
            (pd.to_datetime(self.pricing_data['effective_from']).dt.date <= current_date) &
            (pd.to_datetime(self.pricing_data['effective_to']).dt.date >= current_date)
        ]
        
        # 1. 精确匹配：产品类型 + 规范化型号 + 期数
        exact_match = valid_configs[
            (valid_configs['product_type'] == product_type) &
            (valid_configs['model_norm'] == normalized_model) &
            (valid_configs['periods'] == periods)
        ]
        
        if not exact_match.empty:
            logger.info(f"找到精确匹配的定价配置: {product_type}, {normalized_model}, {periods}期")
            return exact_match.iloc[0].to_dict()
        
        # 2. 别名匹配：检查model_aliases字段
        for _, config in valid_configs.iterrows():
            if (config['product_type'] == product_type and 
                config['periods'] == periods and 
                config['model_aliases']):
                
                aliases = [alias.strip() for alias in str(config['model_aliases']).split(',')]
                normalized_aliases = [self.normalize_model_name(alias) for alias in aliases]
                
                if normalized_model in normalized_aliases:
                    logger.info(f"找到别名匹配的定价配置: {product_type}, {normalized_model}, {periods}期")
                    return config.to_dict()
        
        # 3. 默认匹配：相同产品类型和期数的默认配置
        default_match = valid_configs[
            (valid_configs['product_type'] == product_type) &
            (valid_configs['periods'] == periods) &
            (valid_configs['model_norm'].str.contains('默认|通用|default', case=False, na=False))
        ]
        
        if not default_match.empty:
            logger.info(f"找到默认匹配的定价配置: {product_type}, {periods}期")
            return default_match.iloc[0].to_dict()
        
        logger.warning(f"未找到匹配的定价配置: {product_type}, {model_name}, {periods}期")
        return None
    
    def get_all_configs(self) -> Optional[pd.DataFrame]:
        """
        获取所有定价配置
        
        Returns:
            定价配置DataFrame
        """
        return self.pricing_data.copy() if self.pricing_data is not None else None
    
    def reload_config(self) -> bool:
        """
        重新加载配置（热加载）
        
        Returns:
            是否重新加载成功
        """
        logger.info("正在重新加载定价配置...")
        self.config_loaded = False
        return self.load_pricing_config()
    
    def validate_config(self) -> List[str]:
        """
        验证配置文件的完整性
        
        Returns:
            验证错误列表
        """
        errors = []
        
        if self.pricing_data is None:
            errors.append("定价配置未加载")
            return errors
        
        # 检查必需字段
        required_fields = [
            'product_type', 'model_norm', 'periods', 'billing_months',
            'scheme_version', 'rent_per_period', 'buyout_total',
            'downpayment_default', 'effective_from', 'effective_to'
        ]
        
        for field in required_fields:
            if field not in self.pricing_data.columns:
                errors.append(f"缺少必需字段: {field}")
        
        # 检查数据类型
        for _, config in self.pricing_data.iterrows():
            try:
                # 检查数值字段
                float(config.get('rent_per_period', 0))
                float(config.get('buyout_total', 0))
                float(config.get('downpayment_default', 0))
                int(config.get('periods', 0))
                int(config.get('billing_months', 0))
                
                # 检查日期字段
                pd.to_datetime(config.get('effective_from'))
                pd.to_datetime(config.get('effective_to'))
                
            except (ValueError, TypeError) as e:
                errors.append(f"配置行数据类型错误: {config.get('model_norm', 'unknown')} - {str(e)}")
        
        return errors
