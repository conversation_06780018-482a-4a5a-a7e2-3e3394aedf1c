#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Excel数据读取模块
负责读取和解析Excel文件中的三张表：订单管理、资金流水账、@芳会资料补充
"""

import pandas as pd
import logging
from typing import Dict, Tuple, Optional, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class ExcelReader:
    """Excel文件读取器"""
    
    def __init__(self, file_path: str):
        """
        初始化Excel读取器
        
        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        self.workbook_data = {}
    
    def read_all_sheets(self) -> Dict[str, pd.DataFrame]:
        """
        读取Excel文件中的所有工作表
        
        Returns:
            包含所有工作表数据的字典
        """
        try:
            # 读取所有工作表
            all_sheets = pd.read_excel(self.file_path, sheet_name=None, engine='openpyxl')
            
            # 标准化工作表名称
            standardized_sheets = {}
            for sheet_name, df in all_sheets.items():
                # 去除空白字符并标准化名称
                clean_name = sheet_name.strip()
                standardized_sheets[clean_name] = df
            
            self.workbook_data = standardized_sheets
            logger.info(f"成功读取Excel文件，包含工作表: {list(standardized_sheets.keys())}")
            
            return standardized_sheets
            
        except Exception as e:
            logger.error(f"读取Excel文件失败: {e}")
            raise
    
    def get_order_management_data(self) -> Optional[pd.DataFrame]:
        """
        获取订单管理数据
        
        Returns:
            订单管理DataFrame，如果不存在则返回None
        """
        sheet_names = ['订单管理', '订单管理表', 'Order Management']
        
        for name in sheet_names:
            if name in self.workbook_data:
                df = self.workbook_data[name].copy()
                logger.info(f"找到订单管理数据，共 {len(df)} 行")
                return self._clean_order_data(df)
        
        logger.warning("未找到订单管理工作表")
        return None
    
    def get_transaction_data(self) -> Optional[pd.DataFrame]:
        """
        获取资金流水账数据
        
        Returns:
            资金流水账DataFrame，如果不存在则返回None
        """
        sheet_names = ['资金流水账', '流水账', 'Transaction', 'Transactions']
        
        for name in sheet_names:
            if name in self.workbook_data:
                df = self.workbook_data[name].copy()
                logger.info(f"找到资金流水账数据，共 {len(df)} 行")
                return self._clean_transaction_data(df)
        
        logger.warning("未找到资金流水账工作表")
        return None
    
    def get_customer_supplement_data(self) -> Optional[pd.DataFrame]:
        """
        获取@芳会资料补充数据
        
        Returns:
            客户补充资料DataFrame，如果不存在则返回None
        """
        sheet_names = ['@芳会资料补充', '芳会资料补充', '客户资料', 'Customer Info']
        
        for name in sheet_names:
            if name in self.workbook_data:
                df = self.workbook_data[name].copy()
                logger.info(f"找到客户补充资料数据，共 {len(df)} 行")
                return self._clean_customer_data(df)
        
        logger.warning("未找到客户补充资料工作表")
        return None
    
    def _clean_order_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清理订单管理数据
        
        Args:
            df: 原始订单数据
            
        Returns:
            清理后的订单数据
        """
        # 删除完全空白的行
        df = df.dropna(how='all')
        
        # 标准化列名
        column_mapping = {
            '订单编号': 'order_number',
            '订单日期': 'order_date', 
            '客户姓名': 'customer_name',
            '店铺归属': 'shop_affiliation',
            '产品类型': 'product_type',
            '台数': 'devices',
            '期数': 'periods',
            '每期还款金': 'monthly_payment',
            '合同金额': 'contract_amount',
            '总待收': 'total_receivable',
            '备注': 'remarks'
        }
        
        # 重命名存在的列
        for old_name, new_name in column_mapping.items():
            if old_name in df.columns:
                df = df.rename(columns={old_name: new_name})
        
        # 处理日期列
        date_columns = ['order_date']
        for col in date_columns:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], errors='coerce')
        
        # 处理数值列
        numeric_columns = ['devices', 'periods', 'monthly_payment', 'contract_amount', 'total_receivable']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 处理期次日期列（第1期到第6期）
        for i in range(1, 7):
            period_col = f'第{i}期'
            if period_col in df.columns:
                df[f'period_{i}_date'] = pd.to_datetime(df[period_col], errors='coerce')
        
        return df
    
    def _clean_transaction_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清理资金流水账数据
        
        Args:
            df: 原始交易数据
            
        Returns:
            清理后的交易数据
        """
        # 删除完全空白的行
        df = df.dropna(how='all')
        
        # 标准化列名
        column_mapping = {
            '日期': 'transaction_date',
            '订单编号': 'order_number',
            '交易类型': 'transaction_type',
            '交易金额': 'amount',
            '归属期数': 'period_number',
            '交易订单号': 'transaction_order_number',
            '资金流向': 'fund_flow',
            '可用余额': 'available_balance',
            '待出金': 'pending_withdrawal',
            '备注': 'remarks'
        }
        
        # 重命名存在的列
        for old_name, new_name in column_mapping.items():
            if old_name in df.columns:
                df = df.rename(columns={old_name: new_name})
        
        # 处理日期列
        if 'transaction_date' in df.columns:
            df['transaction_date'] = pd.to_datetime(df['transaction_date'], errors='coerce')
        
        # 处理数值列
        numeric_columns = ['amount', 'available_balance', 'pending_withdrawal']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        return df
    
    def _clean_customer_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清理客户补充资料数据
        
        Args:
            df: 原始客户数据
            
        Returns:
            清理后的客户数据
        """
        # 删除完全空白的行
        df = df.dropna(how='all')
        
        # 标准化列名
        column_mapping = {
            '订单编号': 'order_number',
            '客户姓名': 'customer_name',
            '手机号': 'phone',
            '客服人员': 'customer_service',
            '业务归属': 'business_affiliation',
            '备注': 'remarks'
        }
        
        # 重命名存在的列
        for old_name, new_name in column_mapping.items():
            if old_name in df.columns:
                df = df.rename(columns={old_name: new_name})
        
        return df
    
    def validate_required_data(self) -> Tuple[bool, str]:
        """
        验证必需的数据是否存在
        
        Returns:
            (是否验证通过, 错误信息)
        """
        if not self.workbook_data:
            return False, "请先读取Excel文件"
        
        # 检查订单管理数据
        order_data = self.get_order_management_data()
        if order_data is None or order_data.empty:
            return False, "缺少订单管理数据"
        
        # 检查必需字段
        required_order_fields = ['order_number', 'customer_name', 'product_type']
        missing_fields = [field for field in required_order_fields if field not in order_data.columns]
        if missing_fields:
            return False, f"订单管理数据缺少必需字段: {missing_fields}"
        
        # 检查资金流水账数据
        transaction_data = self.get_transaction_data()
        if transaction_data is None or transaction_data.empty:
            return False, "缺少资金流水账数据"
        
        return True, "数据验证通过"
