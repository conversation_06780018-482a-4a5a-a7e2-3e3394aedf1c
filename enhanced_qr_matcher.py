#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
from datetime import timed<PERSON>ta
from typing import Dict, List, Tuple
from qr_transaction_extractor import QRTransactionExtractor


class EnhancedQRMatcher:
    """增强版聚合二维码交易流水匹配器"""
    
    def __init__(self, tz_file: str, ls_file: str):
        """
        初始化匹配器
        
        Args:
            tz_file: TZ.xlsx文件路径
            ls_file: LS.xlsx文件路径
        """
        self.tz_file = tz_file
        self.ls_file = ls_file
        self.extractor = QRTransactionExtractor(tz_file)
        self.qr_df = None
        self.ls_df = None
        self.original_indices = []
        
    def load_data(self) -> bool:
        """加载数据"""
        try:
            # 1. 提取聚合二维码数据
            self.qr_df, self.original_indices = self.extractor.load_and_extract()
            if self.qr_df.empty:
                print("❌ 没有可匹配的聚合二维码交易")
                return False
            
            # 2. 加载LS数据
            print("📂 正在加载LS.xlsx...")
            self.ls_df = pd.read_excel(self.ls_file, sheet_name='Sheet1')
            print(f"   LS表格加载成功，共 {len(self.ls_df)} 条记录")
            
            # 3. 数据预处理
            self._preprocess_data()
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def _preprocess_data(self):
        """预处理数据"""
        print("🔧 正在预处理数据...")
        
        # 处理聚合二维码数据时间
        self.qr_df['支付时间_dt'] = self._parse_datetime_flexible(
            self.qr_df['支付时间'], '聚合二维码支付时间'
        )
        self.qr_df['支付时间_分钟'] = (
            self.qr_df['支付时间_dt'].dt.floor('min')
        )
        
        # 处理LS数据
        if '交易时间' in self.ls_df.columns:
            self.ls_df['交易时间_dt'] = self._parse_datetime_flexible(
                self.ls_df['交易时间'], 'LS交易时间'
            )
            self.ls_df['交易时间_分钟'] = (
                self.ls_df['交易时间_dt'].dt.floor('min')
            )
        
        # 确保金额为数值类型
        self.qr_df['金额'] = pd.to_numeric(self.qr_df['金额'], errors='coerce')
        if '交易金额' in self.ls_df.columns:
            self.ls_df['交易金额'] = pd.to_numeric(
                self.ls_df['交易金额'], errors='coerce'
            )
        
        print("   数据预处理完成")
    
    def _parse_datetime_flexible(self, datetime_series, column_name: str):
        """灵活解析日期时间"""
        print(f"   正在解析{column_name}...")
        
        parsed_series = pd.Series(dtype='datetime64[ns]', 
                                 index=datetime_series.index)
        
        for i, time_str in enumerate(datetime_series):
            if pd.isna(time_str):
                continue
            
            try:
                parsed_dt = pd.to_datetime(str(time_str), errors='coerce')
                if not pd.isna(parsed_dt):
                    parsed_series.iloc[i] = parsed_dt
            except Exception:
                continue
        
        success_count = len(parsed_series.dropna())
        total_count = len(datetime_series.dropna())
        print(f"   {column_name}解析完成：{success_count}/{total_count} 条成功")
        
        return parsed_series
    
    def find_matches(self) -> pd.DataFrame:
        """查找匹配的交易记录"""
        print("🔍 正在查找匹配的聚合二维码交易记录...")
        
        all_candidates = []
        
        for qr_idx, qr_row in self.qr_df.iterrows():
            qr_amount = qr_row['金额']
            qr_time = qr_row['支付时间_分钟']
            
            if pd.isna(qr_amount) or pd.isna(qr_time):
                continue
            
            # 查找金额匹配的LS记录
            amount_matches = self.ls_df[
                (abs(self.ls_df['交易金额'] - qr_amount) < 0.01)
            ]
            
            if amount_matches.empty:
                continue
            
            # 查找时间符合条件的记录
            for ls_idx, ls_row in amount_matches.iterrows():
                ls_time = ls_row['交易时间_分钟']
                
                if pd.isna(ls_time):
                    continue
                
                # 检查时间条件：QR支付时间要晚于LS时间，且在24小时内
                time_diff = qr_time - ls_time
                
                if timedelta(0) <= time_diff <= timedelta(days=1):
                    # 计算综合优先级分数
                    priority_score = self._calculate_priority_score(
                        time_diff, qr_amount, qr_row, ls_row
                    )
                    
                    candidate = {
                        'qr_idx': qr_idx,
                        'ls_idx': ls_idx,
                        'customer_name': qr_row['姓名'],
                        'amount': qr_amount,
                        'qr_time': qr_row['支付时间_dt'],
                        'ls_time': ls_row['交易时间_dt'],
                        'time_diff': time_diff,
                        'ls_serial': ls_row['交易参考号'],
                        'priority_score': priority_score,
                        'original_index': qr_row['_original_index']
                    }
                    all_candidates.append(candidate)
        
        print(f"   找到 {len(all_candidates)} 个候选匹配项")
        
        if not all_candidates:
            print("❌ 未找到任何匹配项")
            return pd.DataFrame()
        
        # 解决冲突，选择最优匹配
        final_matches = self._resolve_conflicts(all_candidates)
        
        matches_df = pd.DataFrame(final_matches)
        
        if not matches_df.empty:
            print(f"✅ 最终确定 {len(matches_df)} 个匹配项")
            self._print_match_statistics(matches_df)
        
        return matches_df
    
    def _calculate_priority_score(self, time_diff: timedelta, amount: float,
                                 qr_row: pd.Series, ls_row: pd.Series) -> float:
        """计算匹配优先级分数"""
        
        # 1. 时间优先级 (40%)
        time_diff_hours = time_diff.total_seconds() / 3600
        time_priority = max(0, 1.0 - (time_diff_hours / 24.0))
        
        # 2. 金额优先级 (25%)
        if amount >= 10000:
            amount_priority = 1.0
        elif amount >= 5000:
            amount_priority = 0.9
        elif amount >= 1000:
            amount_priority = 0.8
        else:
            amount_priority = 0.7
        
        # 3. 客户唯一性 (20%)
        customer_uniqueness = self._check_customer_uniqueness(qr_row, amount)
        
        # 4. 订单完整性 (15%)
        order_completeness = 1.0 if pd.notna(qr_row.get('备注')) else 0.5
        
        # 综合评分
        priority_score = (
            time_priority * 0.40 +
            amount_priority * 0.25 + 
            customer_uniqueness * 0.20 +
            order_completeness * 0.15
        )
        
        return priority_score
    
    def _check_customer_uniqueness(self, qr_row: pd.Series, amount: float) -> float:
        """检查客户当天该金额交易的唯一性"""
        customer_name = qr_row['姓名']
        qr_date = str(qr_row['支付时间'])[:10]  # 提取日期部分
        
        # 查找同客户同日期同金额的交易数量
        same_customer_amount = self.qr_df[
            (self.qr_df['姓名'] == customer_name) &
            (self.qr_df['支付时间'].str[:10] == qr_date) &
            (abs(self.qr_df['金额'] - amount) < 0.01)
        ]
        
        # 如果该客户当天只有一笔该金额交易，优先级提高
        return 1.0 if len(same_customer_amount) == 1 else 0.8
    
    def _resolve_conflicts(self, all_candidates: List[Dict]) -> List[Dict]:
        """解决匹配冲突，确保每个LS流水号只用一次"""
        
        # 按LS流水号分组
        ls_groups = {}
        for candidate in all_candidates:
            ls_serial = candidate['ls_serial']
            if ls_serial not in ls_groups:
                ls_groups[ls_serial] = []
            ls_groups[ls_serial].append(candidate)
        
        final_matches = []
        rejected_count = 0
        
        for ls_serial, candidates in ls_groups.items():
            if len(candidates) == 1:
                # 无冲突
                final_matches.append(candidates[0])
            else:
                # 有冲突，选择优先级最高的
                best_candidate = max(candidates, 
                                   key=lambda x: x['priority_score'])
                final_matches.append(best_candidate)
                
                # 记录被拒绝的候选
                rejected = [c for c in candidates if c != best_candidate]
                rejected_count += len(rejected)
                
                print(f"⚠️  冲突解决: LS流水 {ls_serial}")
                print(f"   选择: {best_candidate['customer_name']} "
                      f"{best_candidate['amount']}元 "
                      f"(优先级:{best_candidate['priority_score']:.3f})")
                for r in rejected:
                    print(f"   拒绝: {r['customer_name']} "
                          f"{r['amount']}元 "
                          f"(优先级:{r['priority_score']:.3f})")
        
        if rejected_count > 0:
            print(f"   冲突解决完成，拒绝了 {rejected_count} 个低优先级匹配")
        
        return final_matches
    
    def _print_match_statistics(self, matches_df: pd.DataFrame):
        """打印匹配统计信息"""
        total_qr = len(self.qr_df)
        matched_count = len(matches_df)
        
        # 按优先级分档统计
        high_priority = len(matches_df[matches_df['priority_score'] >= 0.8])
        medium_priority = len(matches_df[
            (matches_df['priority_score'] >= 0.6) & 
            (matches_df['priority_score'] < 0.8)
        ])
        low_priority = len(matches_df[matches_df['priority_score'] < 0.6])
        
        print(f"   聚合二维码交易总数: {total_qr}")
        print(f"   成功匹配数: {matched_count}")
        print(f"   匹配成功率: {matched_count/total_qr*100:.1f}%")
        print(f"   高优先级匹配: {high_priority} 个")
        print(f"   中优先级匹配: {medium_priority} 个")
        print(f"   低优先级匹配: {low_priority} 个")
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        if self.qr_df is None:
            return {}
        
        return self.extractor.get_statistics()


def main():
    """测试函数"""
    print("🚀 增强版聚合二维码匹配器测试")
    print("=" * 50)
    
    matcher = EnhancedQRMatcher("TZ.xlsx", "LS.xlsx")
    
    if not matcher.load_data():
        return
    
    # 显示提取统计
    stats = matcher.get_statistics()
    if stats:
        print(f"\n📊 数据统计:")
        print(f"   总记录数: {stats['total_records']}")
        print(f"   聚合二维码记录数: {stats['qr_records']}")
        print(f"   占比: {stats['qr_percentage']}")
    
    # 执行匹配
    matches = matcher.find_matches()
    
    if not matches.empty:
        print(f"\n👀 前3个匹配结果预览:")
        preview_cols = ['customer_name', 'amount', 'priority_score', 'ls_serial']
        preview_data = matches.head(3)[preview_cols]
        print(preview_data.to_string(index=False))
    
    print("\n🎉 匹配器测试完成！")


if __name__ == "__main__":
    main() 