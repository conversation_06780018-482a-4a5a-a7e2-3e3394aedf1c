# 资金流水账单匹配系统需求规格说明书

## 1. 项目概述

### 1.1 背景介绍
企业在日常财务管理过程中，需要将客户的支付流水与系统内订单进行精确匹配，以确保财务数据的准确性和完整性。由于客户可能存在多笔订单和不同期次的还款，手动匹配工作量大且容易出错，因此需要开发一套自动化的匹配系统。

### 1.2 项目目标
开发一个资金流水账单匹配系统，能够根据既定规则自动匹配客户支付流水与订单数据，提高匹配准确率和工作效率，减少人工干预，同时保留人工确认环节以确保匹配结果的准确性。

## 2. 功能需求

### 2.1 数据来源
- **支付流水数据**：以Excel格式提供，包含客户支付的时间、金额、交易流水号等信息
- **订单数据**：存储在SQLite数据库(.db)中，包含订单编号、账单日期、客户姓名、账单状态等信息

### 2.2 核心功能需求

#### 2.2.1 数据匹配规则
1. **客户姓名匹配**：系统首先通过客户姓名进行精确匹配，作为初步筛选条件，有效缩小潜在匹配订单的范围
2. **账单状态筛选**：系统将自动筛选出符合条件的订单，仅保留状态为"逾期未还"、"待支付"或"未到期"的订单记录进入下一步匹配流程
3. **时间相关度匹配**：对通过前两步筛选的订单，系统将计算支付时间与账单到期日期之间的时间差值，并根据时间接近度分配置信度评分
4. **多订单处理**：当系统识别出多个具有相同置信度评分的订单时，将采用智能分配算法，按照以下规则处理支付金额：
   - 将支付总额在多个订单间进行均等分配
   - 对于无法整除的金额，系统将对每个订单的分配额向下取整
   - 剩余的尾差金额将自动分配至最后一条匹配订单中

#### 2.2.2 人工确认机制
1. **匹配结果实时展示**：系统在完成每条支付流水的自动匹配后，将在终端界面以结构化格式呈现详细匹配结果，包括匹配订单信息、置信度评分及相关账单详情
2. **用户交互式确认**：系统提供直观的确认选项，允许财务人员对匹配结果进行专业审核与确认
3. **数据持久化处理**：用户确认后，系统自动将完整匹配结果写入原始Excel表格，确保数据一致性和可追溯性
4. **异常处理机制**：当用户否决系统匹配结果时，系统将启动辅助匹配流程，引导用户输入正确的订单编号，并执行手动匹配操作
5. **流程自动化衔接**：完成当前记录处理后，系统自动进入下一条支付流水的匹配流程，保证工作连续性

#### 2.2.3 结果输出
1. **结构化数据输出**：系统将在原始Excel表格中自动创建标准化的新增数据列，包含以下关键信息：
   - **订单标识信息**：匹配成功的订单编号（唯一标识符）
   - **期次管理信息**：对应订单的账单期次详情，包括期数、总期数等
   - **匹配质量指标**：系统计算的匹配置信度评分，用于评估匹配准确性
   - **订单关键属性**：包括订单状态、应还金额、到期日期等核心业务数据
   - **多订单匹配记录**：当一笔支付流水匹配到多个订单时，系统将以标准化格式记录所有匹配订单信息，确保数据完整性

### 2.3 数据库交互需求
系统需要与SQLite数据库进行交互，主要涉及以下表结构：

#### 2.3.1 订单表（orders）
包含订单基本信息，如订单编号、客户姓名、订单日期等

#### 2.3.2 还款计划表（payment_schedules）
包含每个订单的还款计划，记录期次、应还日期、应还金额、已还金额和还款状态等

#### 2.3.3 交易表（transactions）
记录所有交易信息，包括交易日期、交易金额、交易流水号等

#### 2.3.4 客户信息表（customer_info）
存储客户的详细信息，与订单表建立一对一关系

## 3. 技术需求

### 3.1 开发环境
- 编程语言：Python
- 数据处理库：Pandas
- 数据库访问：SQLite
- Excel处理：openpyxl、xlrd
- 用户界面：命令行或简易Web界面（Flask框架）

### 3.2 系统架构
1. **数据读取模块**：负责从Excel读取支付流水数据，从数据库读取订单数据
2. **匹配算法模块**：实现核心匹配逻辑
3. **用户交互模块**：展示匹配结果并获取用户确认
4. **数据输出模块**：将确认后的匹配结果写入Excel

### 3.3 数据流程
1. 读取支付流水Excel数据
2. 连接数据库获取订单相关数据
3. 根据匹配规则生成匹配结果
4. 显示匹配结果并等待用户确认
5. 确认后将结果写入原Excel文件
6. 重复处理直至所有支付流水处理完毕

## 4. 非功能需求

### 4.1 性能需求
- 处理单条支付流水的匹配时间不超过5秒
- 支持处理至少1000条支付流水记录的Excel文件

### 4.2 安全需求
- 确保数据库连接的安全性
- 不得对原始数据库内容进行修改

### 4.3 可用性需求
- 提供清晰的操作指引
- 在匹配结果显示时提供足够的信息辅助用户决策
- 支持中断后继续处理剩余数据的功能

## 5. 接口需求

### 5.1 数据输入接口
- 支持Excel(.xlsx/.xls)格式的支付流水数据
- 支持SQLite(.db)格式的订单数据库

### 5.2 用户交互接口
- 命令行交互界面，显示匹配结果和确认选项（表格形式展示）
+------------------------------------------------+
|                  支付流水信息                    |
+---------------+------------------+-------------+
| 交易日期      | 2023-05-10       | 交易号      |
| 客户姓名      | 张三             | 金额        |
| 交易类型      | 租金             | 备注        |
+---------------+------------------+-------------+

+----------------------------------------------+
|                 匹配订单信息                  |
+----+----------+-------------+---------------+
| ID | 订单编号  | 客户姓名    | 应还金额      |
+----+----------+-------------+---------------+
| 1  | OR12345  | 张三        | ¥1,000.00     |
| 2  | OR12346  | 张三        | ¥2,000.00     |
+----+----------+-------------+---------------+

匹配置信度: [████████░░] 80%

请选择操作：
1. 确认匹配结果 [Y]
2. 手动选择匹配 [M]
3. 跳过此记录 [S]

### 5.3 数据输出接口
- 将匹配结果输出到原Excel文件中新增的列
- 提供匹配结果摘要报表（可选）

## 6. 约束条件

### 6.1 业务约束
- 匹配必须遵循指定的业务规则
- 必须保留人工确认环节，确保匹配准确性

### 6.2 技术约束
- 系统需在Windows操作系统上运行
- 仅使用Python及其相关库进行开发

## 7. 验收标准

### 7.1 功能验收
- 成功读取支付流水Excel和订单数据库
- 正确执行匹配规则
- 成功实现人工确认机制
- 正确将结果写入Excel

### 7.2 性能验收
- 匹配速度符合性能需求
- 正确处理边界情况（如多订单匹配）

### 7.3 用户体验验收
- 操作流程清晰
- 匹配结果展示清晰易懂

## 8. 未来扩展

### 8.1 可能的扩展功能
- 批量确认功能
- 更高级的匹配算法
- 图形化用户界面
- 匹配结果统计分析功能

## 9. 术语表

| 术语 | 定义 |
|------|------|
| 支付流水 | 客户的支付记录，包含支付时间、金额、交易流水号等信息 |
| 订单 | 系统中记录的客户订单，包含订单编号、账单日期等信息 |
| 匹配置信度 | 系统判断支付流水与订单匹配程度的信心指数 |
| 账单状态 | 订单账单的当前状态，如"逾期未支付"、"待支付"、"未到期"等 |



## 1. 主要业务表结构

### 1.1 订单表（orders）
| 字段名             | 类型         | 说明                   |
|--------------------|--------------|------------------------|
| id                 | Integer      | 主键，自增             |
| order_date         | Date         | 订单日期               |
| order_number       | String(50)   | 订单编号，唯一索引     |
| customer_name      | String(100)  | 客户姓名，索引         |
| model              | String(100)  | 型号                   |
| customer_attribute | String(50)   | 客户属性               |
| usage              | String(100)  | 用途                   |
| payment_cycle      | String(50)   | 还款周期               |
| product_type       | String(50)   | 产品类型，索引         |
| periods            | Integer      | 期数                   |
| business_type      | String(50)   | 业务类型               |
| total_receivable   | Float        | 总应收                 |
| current_receivable | Float        | 当前应收               |
| remarks            | Text         | 备注                   |
| cost               | Float        | 成本，自动计算         |
| shop_affiliation   | String(100)  | 店铺归属               |

### 1.2 还款计划表（payment_schedules）
| 字段名         | 类型       | 说明                             |
|----------------|------------|----------------------------------|
| id             | Integer    | 主键，自增                       |
| order_id       | Integer    | 订单ID，外键（orders.id）         |
| period_number  | Integer    | 期数                             |
| due_date       | Date       | 应还日期                         |
| amount         | Float      | 应还金额                         |
| paid_amount    | Float      | 已还金额，自动统计                |
| status         | String(20) | 还款状态（未到期、按时还款等）    |

### 1.3 交易表（transactions）
| 字段名                    | 类型         | 说明                         |
|---------------------------|--------------|------------------------------|
| id                        | Integer      | 主键，自增                   |
| transaction_date          | Date         | 交易日期                     |
| order_id                  | Integer      | 订单ID，外键（orders.id）     |
| customer_name             | String(100)  | 客户姓名                     |
| model                     | String(100)  | 型号                         |
| customer_attribute        | String(50)   | 客户属性                     |
| usage                     | String(100)  | 用途                         |
| payment_cycle             | String(50)   | 还款周期                     |
| product_type              | String(50)   | 产品类型                     |
| amount                    | Float        | 交易金额                     |
| period_number             | String(50)   | 归属期数                     |
| transaction_type          | String(50)   | 交易类型                     |
| direction                 | String(50)   | 资金方向                     |
| transaction_order_number  | String(50)   | 交易流水号                   |
| available_balance         | Float        | 可用余额                     |
| pending_withdrawal        | Float        | 待提现金额                   |
| remarks                   | Text         | 备注                         |

### 1.4 客户信息表（customer_info）
| 字段名             | 类型         | 说明                         |
|--------------------|--------------|------------------------------|
| id                 | Integer      | 主键，自增                   |
| order_id           | Integer      | 订单ID，唯一外键（orders.id） |
| order_number       | String(50)   | 订单编号，唯一索引           |
| customer_name      | String(100)  | 客户姓名                     |
| phone              | String(20)   | 手机号码                     |
| rental_period      | String(50)   | 租期                         |
| customer_service   | String(50)   | 客服归属                     |
| business_affiliation | String(50) | 业务归属                     |
| remarks            | Text         | 备注                         |

---

## 2. 表间关系与ER图

``mermaid
erDiagram
    orders ||--o{ payment_schedules : "订单ID"
    orders ||--o{ transactions      : "订单ID"
    orders ||--|| customer_info     : "订单ID"
```

- 一个订单（orders）可以对应多个还款计划（payment_schedules）
- 一个订单（orders）可以有多条资金流水（transactions）
- 一个订单（orders）对应一条客户信息（customer_info）

---

## 3. 字段详细说明与业务含义

### 3.1 订单表 orders
- `cost`：自动计算，包含放款和供应商利润等相关支出。
- `shop_affiliation`：Excel中“店铺名称”字段。

### 3.2 还款计划表 payment_schedules
- `paid_amount`：自动统计每期已还款总额。
- `status`：常见值有“未到期”“按时还款”“逾期未还”“逾期还款”“提前还款”“协商结清”。

### 3.3 交易表 transactions
- `product_type`：与订单表一致。
- `period_number`：部分为数字，部分为空或特殊标记。
- `transaction_type`：如首付款、租金、尾款、放款、供应商利润等。
- `direction`：区分收入/支出。
- `transaction_order_number`：交易流水号。
- `available_balance`/`pending_withdrawal`：部分业务场景使用。

### 3.4 客户信息表 customer_info
- `remarks`：通常为Excel“@芳会资料补充”中的备注。




graph TD
    A[数据输入] --> B[核心处理引擎]
    B --> C[用户交互]
    C --> D[数据输出]
    
    subgraph 核心处理引擎
        B1[数据加载模块]
        B2[匹配算法模块]
        B3[异常处理模块]
    end
    
    subgraph 数据流
        A1[支付流水 Excel] --> B1
        A2[SQLite 数据库] --> B1
        B1 --> B2
        B2 --> C1[匹配结果展示]
        C1 --> D1[结果写入 Excel]
        B3 --> C2[手动匹配界面]
    end