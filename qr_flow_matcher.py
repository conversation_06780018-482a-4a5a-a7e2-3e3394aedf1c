#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
聚合二维码交易流水匹配器 - 主程序

功能：
1. 从TZ.xlsx中提取聚合二维码交易
2. 与LS.xlsx中的银行流水进行智能匹配
3. 将匹配结果合并回原始TZ.xlsx
4. 生成详细的匹配报告

作者：Assistant
版本：v1.0
"""

import sys
import traceback
from enhanced_qr_matcher import EnhancedQRMatcher
from qr_data_merger import QRDataMerger


def check_files_exist():
    """检查必要文件是否存在"""
    import os
    
    required_files = ['TZ.xlsx', 'LS.xlsx']
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        print("\n请确保以下文件在当前目录中:")
        print("   - TZ.xlsx (包含聚合二维码交易数据)")
        print("   - LS.xlsx (包含银行流水数据)")
        return False
    
    return True


def display_welcome():
    """显示欢迎信息"""
    print("🚀 聚合二维码交易流水匹配器 v1.0")
    print("=" * 60)
    print("功能：智能匹配TZ.xlsx中的聚合二维码交易与LS.xlsx中的银行流水")
    print("特色：基于优先级策略，确保最优匹配结果")
    print("输出：完整匹配结果 + 详细分析报告")
    print("=" * 60)


def confirm_execution(stats_info):
    """确认是否执行匹配"""
    print("\n📋 数据概览:")
    print(f"   TZ.xlsx总记录数: {stats_info.get('total_records', 0)}")
    print(f"   聚合二维码记录数: {stats_info.get('qr_records', 0)}")
    print(f"   聚合二维码占比: {stats_info.get('qr_percentage', '0%')}")
    
    if stats_info.get('qr_records', 0) == 0:
        print("\n❌ 没有找到聚合二维码交易记录，无法进行匹配")
        return False
    
    print(f"\n🎯 匹配策略:")
    print(f"   - 时间优先级: 40% (时间差越小越优先)")
    print(f"   - 金额优先级: 25% (大金额优先)")
    print(f"   - 客户唯一性: 20% (单一客户单日同金额优先)")
    print(f"   - 订单完整性: 15% (有备注信息优先)")
    
    print(f"\n❓ 是否开始执行匹配？")
    choice = input("请输入 y/yes 确认，其他任意键取消: ").lower().strip()
    return choice in ['y', 'yes']


def display_match_results(matches_df, merger_summary):
    """显示匹配结果"""
    if matches_df.empty:
        print("\n❌ 未找到任何匹配项")
        return
    
    print(f"\n✅ 匹配完成！")
    print(f"   成功匹配: {merger_summary.get('matched_qr', 0)} 条")
    print(f"   未匹配: {merger_summary.get('unmatched_qr', 0)} 条")
    print(f"   匹配成功率: {merger_summary.get('match_rate', '0%')}")
    
    # 显示匹配质量分布
    high_priority = len(matches_df[matches_df['priority_score'] >= 0.8])
    medium_priority = len(matches_df[
        (matches_df['priority_score'] >= 0.6) & 
        (matches_df['priority_score'] < 0.8)
    ])
    low_priority = len(matches_df[matches_df['priority_score'] < 0.6])
    
    print(f"\n📊 匹配质量分布:")
    print(f"   高优先级匹配(≥0.8): {high_priority} 条")
    print(f"   中优先级匹配(0.6-0.8): {medium_priority} 条")
    print(f"   低优先级匹配(<0.6): {low_priority} 条")
    
    # 显示前5个匹配结果预览
    print(f"\n👀 匹配结果预览 (前5条):")
    preview_cols = ['customer_name', 'amount', 'priority_score', 'ls_serial']
    preview_data = matches_df.head(5)[preview_cols].copy()
    preview_data['priority_score'] = preview_data['priority_score'].apply(
        lambda x: f"{x:.3f}"
    )
    preview_data.columns = ['客户姓名', '金额', '优先级', 'LS流水号']
    print(preview_data.to_string(index=False))


def display_output_files():
    """显示输出文件信息"""
    print(f"\n📁 生成的文件:")
    print(f"   📊 TZ_完整匹配结果.xlsx - 完整的原始数据+匹配结果")
    print(f"   🎯 聚合二维码匹配结果.xlsx - 仅聚合二维码交易结果")
    print(f"   📈 综合匹配报告.xlsx - 详细分析报告(含统计、已匹配、未匹配等)")
    print(f"   📋 聚合二维码交易记录_提取.xlsx - 提取的原始聚合二维码数据")


def handle_error(error, step_name):
    """处理错误"""
    print(f"\n❌ {step_name}过程中发生错误:")
    print(f"   错误信息: {str(error)}")
    print(f"   详细错误: {traceback.format_exc()}")
    print(f"\n💡 建议:")
    print(f"   1. 检查文件格式是否正确")
    print(f"   2. 确保文件未被其他程序占用")
    print(f"   3. 检查文件权限")
    print(f"   4. 重新启动程序")


def main():
    """主函数"""
    try:
        # 1. 显示欢迎信息
        display_welcome()
        
        # 2. 检查文件
        if not check_files_exist():
            return
        
        # 3. 初始化匹配器
        print("\n🔧 正在初始化匹配器...")
        matcher = EnhancedQRMatcher("TZ.xlsx", "LS.xlsx")
        
        # 4. 加载数据
        print("\n📂 正在加载数据...")
        if not matcher.load_data():
            print("❌ 数据加载失败，程序退出")
            return
        
        # 5. 获取数据统计并确认执行
        stats = matcher.get_statistics()
        if not confirm_execution(stats):
            print("❌ 用户取消操作")
            return
        
        # 6. 执行匹配
        print("\n🔍 正在执行智能匹配...")
        matches = matcher.find_matches()
        
        # 7. 合并结果
        print("\n🔧 正在合并匹配结果...")
        merger = QRDataMerger("TZ.xlsx")
        enhanced_data = merger.merge_match_results(matches)
        
        if enhanced_data.empty:
            print("❌ 数据合并失败")
            return
        
        # 8. 保存结果
        print("\n💾 正在保存结果文件...")
        merger.save_results()
        
        # 9. 显示结果
        merger_summary = merger.get_merge_summary()
        display_match_results(matches, merger_summary)
        
        # 10. 显示输出文件
        display_output_files()
        
        print(f"\n🎉 聚合二维码交易流水匹配完成！")
        
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断操作")
    except Exception as e:
        handle_error(e, "程序执行")
    finally:
        print(f"\n👋 感谢使用聚合二维码交易流水匹配器！")


if __name__ == "__main__":
    main() 