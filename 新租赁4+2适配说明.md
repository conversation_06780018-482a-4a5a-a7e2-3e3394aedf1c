# 账单助手系统 - 新租赁4+2适配说明

## 概述

账单助手系统已成功适配新租赁产品4期（4+2模式），支持处理新租赁、旧租赁和电商三种产品类型的数据导入和处理。

## 新功能特性

### 1. 双模式支持
- **账单到期时间提取模式**：原有功能，从外部系统提取账单到期时间
- **新租赁4+2数据导入模式**：新增功能，支持完整的ETL数据处理流程

### 2. 新租赁4+2业务规则
- **期数结构**：4期租金 + 2期买断金
- **账期跨度**：6个自然月
- **买断金分摊**：第5期=买断金/2，第6期=买断金/2-首付
- **自动补期**：如果Excel中只有前4期日期，系统自动补齐第5、6期

### 3. 智能定价配置
- 支持定价配置文件（`config/pricing.csv`）
- 型号规范化和同义词匹配
- 多级匹配策略：精确匹配 → 别名匹配 → 默认配置

### 4. 完整的导入报告
- 处理摘要统计
- 逐期还款计划详情
- 合同金额校验结果
- 问题和异常列表
- 支持JSON和CSV格式下载

## 系统架构

### 数据库层
- **PostgreSQL**：主数据库，替代原有SQLite
- **表结构**：
  - `orders`：订单表
  - `payment_schedules`：还款计划表（新增delta_amount字段）
  - `transactions`：交易表
  - `customer_info`：客户信息表
  - `import_reports`：导入报告表

### ETL处理层
- **Excel读取器**：解析三张工作表（订单管理、资金流水账、@芳会资料补充）
- **租赁规则引擎**：实现新租赁4+2、旧租赁6期、电商的业务规则
- **定价配置管理器**：处理定价配置和型号匹配
- **ETL处理器**：协调整个ETL流程

### API接口层
- `POST /api/etl/upload`：上传Excel文件并执行ETL处理
- `POST /api/etl/trigger`：触发ETL处理（指定文件路径）
- `GET /api/etl/report`：获取导入报告
- `GET /api/etl/report/download`：下载导入报告
- `GET /api/etl/metrics`：获取ETL指标

## 使用指南

### 1. 环境准备

```bash
# 安装新依赖
pip install -r bill-helper/requirements.txt

# 配置PostgreSQL数据库
# 设置环境变量（可选）
export POSTGRES_HOST=localhost
export POSTGRES_PORT=5432
export POSTGRES_DB=bill_helper
export POSTGRES_USER=postgres
export POSTGRES_PASSWORD=your_password
```

### 2. Excel文件格式要求

#### 订单管理表
必需字段：
- 订单编号（唯一）
- 订单日期
- 客户姓名
- 店铺归属
- 产品类型（租赁/电商）
- 台数
- 期数（新租赁=4，旧租赁=6）

可选字段：
- 每期还款金
- 合同金额/总待收
- 第1期~第6期（日期）
- 备注

#### 资金流水账表
必需字段：
- 日期
- 交易类型（首付款/租金/尾款）
- 交易金额

可选字段：
- 订单编号（强烈建议）
- 归属期数（支持文字类和数字类）
- 备注

#### @芳会资料补充表
- 订单编号
- 客户姓名
- 手机号
- 客服人员
- 业务归属
- 备注

### 3. 定价配置

编辑 `config/pricing.csv` 文件：

```csv
product_type,model_norm,model_aliases,periods,billing_months,scheme_version,rent_per_period,buyout_total,downpayment_default,currency,effective_from,effective_to,shop,remarks
租赁,iPhone15Pro256G,"iPhone 15 Pro 256G,iPhone15Pro256GB",4,6,new,800,1200,600,CNY,2024-01-01,2024-12-31,默认店铺,新租赁4+2配置
```

### 4. 操作步骤

1. **启动系统**
   ```bash
   python bill-helper/app.py
   ```

2. **访问Web界面**
   - 打开浏览器访问：http://localhost:5001

3. **选择功能模式**
   - 选择"新租赁4+2数据导入"模式

4. **上传Excel文件**
   - 选择包含三张工作表的Excel文件
   - 可选择"试运行模式"进行验证

5. **查看处理结果**
   - 查看处理摘要
   - 下载导入报告（JSON/CSV格式）

### 5. API使用示例

```bash
# 上传文件并处理
curl -X POST -F "file=@test_data.xlsx" -F "dry_run=true" http://localhost:5001/api/etl/upload

# 获取报告
curl "http://localhost:5001/api/etl/report?run_id=20240101T120000Z_abc12345"

# 下载CSV报告
curl "http://localhost:5001/api/etl/report/download?run_id=20240101T120000Z_abc12345&format=csv&type=schedules" -o schedules.csv
```

## 业务规则详解

### 新租赁4+2规则
1. **期次结构**：
   - 第1-4期：每期还款金
   - 第5期：买断金/2
   - 第6期：买断金/2 - 首付（如果<0，则为0，差额并入第5期）

2. **日期补齐**：
   - 如果只提供前4期日期，第5期=第4期+1月，第6期=第4期+2月

3. **状态判定**：
   - |应还-实还| ≤ 100：已还清
   - > 100且归属期为文字类：协商结清
   - 其他：部分还款/逾期

### 旧租赁6期规则
1. **期次结构**：
   - 第1-5期：每期还款金
   - 第6期：每期还款金 - 买断金（不足0按0）

### 电商规则
1. **期次结构**：
   - 各期均为每期还款金

## 测试数据

使用提供的示例数据进行测试：

```bash
# 生成测试数据
python sample_data/create_sample_excel.py

# 测试文件位置
# - sample_data/新租赁4+2测试数据.xlsx（ETL导入测试）
# - sample_data/账单提取测试数据.xlsx（账单提取测试）
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查PostgreSQL服务是否启动
   - 验证数据库连接参数
   - 确认数据库用户权限

2. **定价配置未生效**
   - 检查 `config/pricing.csv` 文件是否存在
   - 验证配置文件格式和字段
   - 确认有效期设置

3. **Excel文件解析失败**
   - 检查工作表名称是否正确
   - 验证必需字段是否存在
   - 确认数据格式（日期、数值）

4. **还款计划生成异常**
   - 检查期数设置是否正确
   - 验证租金、首付、买断金数据
   - 查看导入报告中的错误信息

### 日志查看

系统日志包含详细的处理信息，可用于问题诊断：
- ETL处理日志
- 数据库操作日志
- 配置加载日志
- 业务规则执行日志

## 后续计划

1. **性能优化**
   - 大批量数据处理优化
   - 数据库查询性能优化
   - 缓存机制实现

2. **功能增强**
   - 更多型号同义词支持
   - 自定义业务规则配置
   - 数据导出格式扩展

3. **监控和运维**
   - 处理指标监控
   - 异常告警机制
   - 自动化部署流程
