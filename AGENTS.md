# Repository Guidelines

## Project Structure & Module Organization
- Web app: `bill-helper/`
  - Entry: `bill-helper/app.py` (Flask, runs on port 5001)
  - Config: `bill-helper/config.py` (`uploads/`, `processed/`, extensions)
  - Modules: `bill-helper/modules/{auth,crawler,processor,exporter}/`
  - Templates: `bill-helper/templates/`
- CLI tools: `qr_flow_matcher.py`, `enhanced_qr_matcher.py`, `qr_data_merger.py`, `qr_transaction_extractor.py`, `multi_file_filler_correct.py`
- Supporting: `requirements.txt`, `bill-helper/requirements.txt`, `.trunk/` configs, `.vscode/`

## Build, Test, and Development Commands
- Setup (root):
  - `python -m venv .venv && . .venv/Scripts/Activate.ps1` (Windows PowerShell)
  - `pip install -r bill-helper/requirements.txt`
  - `python -m playwright install` (install browsers for crawler)
- Run web app: `python bill-helper/app.py` → visit `http://localhost:5001`
- Run CLI matcher: `python qr_flow_matcher.py` (expects `TZ.xlsx` + `LS.xlsx` in repo root)
- Lint/format (if installed): `ruff check .` and `isort .`

## Coding Style & Naming Conventions
- Python 3.8+; follow PEP 8; use 4-space indentation and descriptive names.
- Imports: group and sort with `isort`; keep unused imports out (ruff will flag).
- Functions/classes: `snake_case`/`PascalCase`; module files: `snake_case.py`.
- Keep UI text/messages user-friendly; comments/docstrings may be Chinese for domain clarity.

## Testing Guidelines
- Framework: prefer `pytest` with tests under `tests/` named `test_*.py`.
- Add focused unit tests for processors/exporters and API route smoke tests.
- Example: `pytest -q` (after `pip install pytest`). Aim to cover key paths for file upload, processing, and export.

## Commit & Pull Request Guidelines
- Commits: use conventional style (`feat:`, `fix:`, `docs:`, `refactor:`, `test:`). Keep commits small and scoped.
- PRs must include:
  - Summary, rationale, and scope
  - Steps to reproduce/verify (commands, sample Excel inputs/outputs)
  - Screenshots or JSON responses for web changes
  - Linked issue/reference if applicable

## Security & Configuration Tips
- Do not commit credentials or real data. The defaults in `config.py` are for local dev only.
- Prefer environment variables for secrets; scrub logs of sensitive values.
- Large files: place sample data only; avoid committing private spreadsheets.
- Playwright automation can be sensitive to site changes—handle errors gracefully and add retries.

