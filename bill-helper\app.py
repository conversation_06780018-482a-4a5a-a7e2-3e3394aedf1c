#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
账单助手系统主应用入口
整合账单到期时间自动化提取工具和数据表格格式化项目的功能
"""

import os
import time
import uuid
import json
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_file
import pandas as pd
from werkzeug.utils import secure_filename
from datetime import datetime

# 导入配置
from config import (
    UPLOAD_FOLDER, PROCESSED_FOLDER, ALLOWED_EXTENSIONS,
    PLATFORM_URL, DEFAULT_USERNAME, DEFAULT_PASSWORD, SYSTEMS
)

# 导入功能模块
from modules.auth.login import login_platform, close_session
from modules.crawler.bill_crawler import fetch_bill_due_dates
from modules.processor.data_processor import extract_order_ids, process_data, format_output
from modules.exporter.excel_exporter import export_to_excel

# 导入新的ETL和数据库模块
from database.connection import init_database, create_tables
from api.etl_routes import etl_bp

# 创建Flask应用
app = Flask(__name__)
app.secret_key = 'bill-helper-secret-key'  # 用于会话安全，生产环境应使用复杂的随机密钥

# 设置文件上传配置
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['PROCESSED_FOLDER'] = PROCESSED_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 限制上传文件大小为16MB

# 注册蓝图
app.register_blueprint(etl_bp)

# 存储任务状态的字典
tasks = {}

def allowed_file(filename):
    """
    检查文件扩展名是否允许
    """
    if not filename or '.' not in filename:
        return False
    
    extension = filename.rsplit('.', 1)[1].lower()
    return extension in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    """
    主页路由
    """
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """
    文件上传处理
    """
    # 检查请求中是否有文件部分
    if 'file' not in request.files:
        return jsonify({'success': False, 'error': '没有选择文件'})
    
    file = request.files['file']
    
    # 如果用户没有选择文件
    if file.filename == '':
        return jsonify({'success': False, 'error': '没有选择文件'})
    
    if file and allowed_file(file.filename):
        # 安全地获取文件名
        original_filename = file.filename
        filename = secure_filename(file.filename)
        
        # 如果secure_filename移除了扩展名，则从原始文件名中提取
        if '.' not in filename and '.' in original_filename:
            # 获取原始扩展名
            original_ext = original_filename.rsplit('.', 1)[1].lower()
            if original_ext in ALLOWED_EXTENSIONS:
                filename = f"{filename}.{original_ext}"
        
        # 生成唯一的文件ID，用于后续处理
        file_id = str(uuid.uuid4())
        
        # 创建唯一的文件路径
        input_path = os.path.join(app.config['UPLOAD_FOLDER'], f"{file_id}_{filename}")
        
        # 保存上传的文件
        file.save(input_path)
        
        # 检查文件中是否包含订单ID列
        try:
            # 确定文件扩展名
            file_extension = os.path.splitext(input_path)[1].lower()
            
            # 添加调试信息
            print(f"调试信息: 原始文件名={original_filename}, 安全文件名={filename}, 扩展名={file_extension}")
            
            if file_extension == '.xls':
                engine = 'xlrd'
            elif file_extension == '.xlsx':
                engine = 'openpyxl'
            else:
                os.remove(input_path)  # 删除不支持的文件
                return jsonify({'success': False, 'error': f'不支持的文件格式: {file_extension}，原始文件名: {original_filename}'})
                
            # 尝试读取文件
            try:
                df = pd.read_excel(input_path, engine=engine)
            except Exception as e:
                # 如果第一次尝试失败，使用另一个引擎
                try:
                    alternate_engine = 'openpyxl' if engine == 'xlrd' else 'xlrd'
                    df = pd.read_excel(input_path, engine=alternate_engine)
                except Exception as nested_e:
                    os.remove(input_path)  # 删除无法读取的文件
                    return jsonify({'success': False, 'error': f'无法读取Excel文件，请确保文件格式正确。错误信息: {str(e)}'})
            
            # 检查是否存在订单ID列
            if '订单ID' not in df.columns:
                os.remove(input_path)  # 删除不符合要求的文件
                return jsonify({'success': False, 'error': '上传的文件中没有"订单ID"列'})
            
            # 文件有效，返回成功信息
            return jsonify({
                'success': True,
                'file_id': file_id,
                'filename': filename,
                'order_count': len(df['订单ID'].dropna().unique())
            })
            
        except Exception as e:
            # 发生错误，删除文件并返回错误信息
            try:
                os.remove(input_path)
            except:
                pass
            return jsonify({'success': False, 'error': f'处理文件时出错: {str(e)}'})
    
    return jsonify({'success': False, 'error': '只支持 .xls 或 .xlsx 文件'})

@app.route('/process', methods=['POST'])
def process():
    """
    处理上传的文件，提取账单到期时间并合并数据
    """
    # 获取表单数据
    username = request.form.get('username', DEFAULT_USERNAME)
    password = request.form.get('password', DEFAULT_PASSWORD)
    system_type = request.form.get('system_type', 'old')
    file_id = request.form.get('file_id', '')
    
    if not file_id:
        return jsonify({'success': False, 'error': '缺少文件ID'})
    
    # 查找上传的文件
    upload_files = [f for f in os.listdir(app.config['UPLOAD_FOLDER']) if f.startswith(f"{file_id}_")]
    if not upload_files:
        return jsonify({'success': False, 'error': '找不到上传的文件'})
    
    input_file = os.path.join(app.config['UPLOAD_FOLDER'], upload_files[0])
    
    # 创建唯一的任务ID
    task_id = str(uuid.uuid4())
    
    # 初始化任务状态
    tasks[task_id] = {
        'status': 'started',
        'progress': 10,
        'status_message': '正在启动处理...',
        'details': '',
        'error': None,
        'input_file': input_file,
        'output_files': [],
        'order_count': 0,
        'bill_count': 0,
        'start_time': datetime.now()
    }
    
    # 启动后台任务
    import threading
    thread = threading.Thread(target=process_task, args=(task_id, input_file, username, password, system_type))
    thread.daemon = True
    thread.start()
    
    return jsonify({'success': True, 'task_id': task_id})

def process_task(task_id, input_file, username, password, system_type='old'):
    """
    后台处理任务
    """
    try:
        # 获取系统配置
        system_config = SYSTEMS.get(system_type, SYSTEMS['old'])
        system_name = system_config['name']
        login_url = system_config['login_url']
        
        # 更新任务状态
        tasks[task_id]['status'] = 'processing'
        tasks[task_id]['status_message'] = f'正在登录{system_name}...'
        tasks[task_id]['progress'] = 15
        
        # 提取订单ID
        order_ids, error = extract_order_ids(input_file)
        if error:
            tasks[task_id]['status'] = 'failed'
            tasks[task_id]['error'] = error
            return
        
        tasks[task_id]['order_count'] = len(order_ids)
        tasks[task_id]['status_message'] = f'成功提取 {len(order_ids)} 个订单ID，正在登录{system_name}...'
        tasks[task_id]['progress'] = 20
        
        # 登录平台
        session, error = login_platform(username, password, login_url)
        if error:
            tasks[task_id]['status'] = 'failed'
            tasks[task_id]['error'] = error
            return
        
        try:
            tasks[task_id]['status_message'] = '登录成功，正在提取账单到期时间...'
            tasks[task_id]['progress'] = 30
            
            # 提取账单到期时间
            order_details_url = system_config['order_details_url']
            due_dates, error = fetch_bill_due_dates(session, order_ids, order_details_url)
            if error:
                tasks[task_id]['status'] = 'failed'
                tasks[task_id]['error'] = error
                return
            
            tasks[task_id]['bill_count'] = len(due_dates)
            tasks[task_id]['status_message'] = f'成功提取 {len(due_dates)} 条账单到期时间信息，正在处理数据...'
            tasks[task_id]['progress'] = 70
            
            # 处理和合并数据
            processed_data, error = process_data(input_file, due_dates)
            if error:
                tasks[task_id]['status'] = 'failed'
                tasks[task_id]['error'] = error
                return
            
            tasks[task_id]['status_message'] = '数据处理完成，正在导出结果...'
            tasks[task_id]['progress'] = 85
            
            # 生成输出文件路径
            current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(app.config['PROCESSED_FOLDER'], f"处理后的订单数据_{current_time}.xlsx")
            
            # 尝试使用模板格式化输出
            template_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '002.xlsx')
            if not os.path.exists(template_path):
                template_path = None
            
            output_path, error = format_output(processed_data, template_path, output_file)
            if error:
                tasks[task_id]['status'] = 'failed'
                tasks[task_id]['error'] = error
                return
            
            # 只添加处理后的订单数据文件到下载列表
            file_info = {
                'id': str(uuid.uuid4()),
                'path': output_path,
                'name': os.path.basename(output_path),
                'type': 'excel'
            }
            tasks[task_id]['output_files'].append(file_info)
            
            # 更新任务状态为完成
            tasks[task_id]['status'] = 'completed'
            tasks[task_id]['status_message'] = '处理完成！'
            tasks[task_id]['progress'] = 100
            
        finally:
            # 确保关闭会话
            close_session(session)
    
    except Exception as e:
        # 处理过程中发生异常
        tasks[task_id]['status'] = 'failed'
        tasks[task_id]['error'] = str(e)
        import traceback
        tasks[task_id]['details'] = traceback.format_exc()

@app.route('/status/<task_id>', methods=['GET'])
def get_status(task_id):
    """
    获取任务处理状态
    """
    if task_id not in tasks:
        return jsonify({'status': 'not_found', 'error': '找不到任务'})
    
    task = tasks[task_id]
    
    # 构建响应数据
    response = {
        'status': task['status'],
        'progress': task['progress'],
        'status_message': task['status_message'],
        'details': task['details'],
        'order_count': task['order_count'],
        'bill_count': task['bill_count']
    }
    
    # 如果任务完成，添加文件信息
    if task['status'] == 'completed':
        response['files'] = task['output_files']
    
    # 如果任务失败，添加错误信息
    if task['status'] == 'failed':
        response['error'] = task['error']
    
    return jsonify(response)

@app.route('/download/<file_id>', methods=['GET'])
def download_file(file_id):
    """
    下载处理结果文件
    """
    # 查找文件
    for task_id, task in tasks.items():
        for file_info in task.get('output_files', []):
            if file_info['id'] == file_id:
                return send_file(
                    file_info['path'],
                    as_attachment=True,
                    download_name=file_info['name']
                )
    
    # 找不到文件
    flash('找不到请求的文件', 'danger')
    return redirect(url_for('index'))

@app.route('/test')
def test():
    """
    测试路由，检查系统是否正常运行
    """
    return jsonify({
        'status': 'ok',
        'message': '账单助手系统正常运行',
        'version': '1.0.0',
        'time': datetime.now().isoformat()
    })

@app.errorhandler(404)
def page_not_found(e):
    """
    404页面未找到处理
    """
    return render_template('index.html'), 404

@app.errorhandler(500)
def server_error(e):
    """
    500服务器错误处理
    """
    return jsonify({'status': 'error', 'message': '服务器内部错误'}), 500

# 启动应用
if __name__ == '__main__':
    # 确保上传和处理目录存在
    os.makedirs(UPLOAD_FOLDER, exist_ok=True)
    os.makedirs(PROCESSED_FOLDER, exist_ok=True)

    # 初始化数据库
    try:
        init_database()
        create_tables()
        print("数据库初始化成功")
    except Exception as e:
        print(f"数据库初始化失败: {e}")
        print("将继续运行，但ETL功能可能不可用")

    # 启动Flask应用
    app.run(debug=True, port=5001)
