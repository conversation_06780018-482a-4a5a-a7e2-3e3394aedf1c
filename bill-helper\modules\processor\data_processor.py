#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据处理模块
负责合并原始订单数据与到期时间数据，处理成所需格式
"""

import pandas as pd
import os
from datetime import datetime


def format_date_to_slash(date_str):
    """
    将日期格式转换为 "2025/1/10" 格式
    
    参数:
    - date_str: 原始日期字符串
    
    返回:
    - 格式化后的日期字符串
    """
    if not date_str or pd.isna(date_str):
        return ''
    
    try:
        # 尝试解析日期
        if isinstance(date_str, str):
            # 处理常见的日期格式
            if len(date_str) >= 10:
                date_str = date_str[:10]  # 只取日期部分，去掉时间
            date_obj = pd.to_datetime(date_str, errors='coerce')
        else:
            date_obj = pd.to_datetime(date_str, errors='coerce')
        
        if pd.notna(date_obj):
            # 格式化为 "年/月/日" 格式，去掉前导零
            return f"{date_obj.year}/{date_obj.month}/{date_obj.day}"
        else:
            return str(date_str)
    except Exception:
        return str(date_str)


def extract_order_ids(input_data):
    """
    从上传的Excel文件中提取订单ID列表
    
    参数:
    - input_data: DataFrame或文件路径
    
    返回:
    - order_ids: 订单ID列表
    - error: 错误信息（如有）
    """
    try:
        # 如果是文件路径，读取为DataFrame
        if isinstance(input_data, str):
            # 确定文件扩展名
            file_extension = os.path.splitext(input_data)[1].lower()
            if file_extension == '.xls':
                engine = 'xlrd'
            elif file_extension == '.xlsx':
                engine = 'openpyxl'
            else:
                return None, f"不支持的文件格式: {file_extension}"
                
            try:
                df = pd.read_excel(input_data, engine=engine)
            except Exception as e:
                # 如果第一次尝试失败，使用另一个引擎
                try:
                    alternate_engine = 'openpyxl' if engine == 'xlrd' else 'xlrd'
                    df = pd.read_excel(input_data, engine=alternate_engine)
                except Exception as nested_e:
                    return None, f"无法读取Excel文件: {str(e)}，备用引擎错误: {str(nested_e)}"
        else:
            # 已经是DataFrame
            df = input_data
        
        # 检查是否存在订单ID列
        if '订单ID' not in df.columns:
            return None, "上传的文件中没有'订单ID'列"
        
        # 提取不重复的订单ID
        order_ids = df['订单ID'].dropna().unique().tolist()
        
        if not order_ids:
            return None, "上传的文件中没有有效的订单ID"
            
        return order_ids, None
    except Exception as e:
        return None, f"提取订单ID时出错: {str(e)}"


def process_data(input_data, due_dates):
    """
    合并原始订单数据与到期时间数据
    
    参数:
    - input_data: 原始订单数据（DataFrame或文件路径）
    - due_dates: 到期时间数据（列表）
    
    返回:
    - processed_data: 处理后的DataFrame
    - error: 错误信息（如有）
    """
    try:
        # 如果是文件路径，读取为DataFrame
        if isinstance(input_data, str):
            # 确定文件扩展名
            file_extension = os.path.splitext(input_data)[1].lower()
            if file_extension == '.xls':
                engine = 'xlrd'
            elif file_extension == '.xlsx':
                engine = 'openpyxl'
            else:
                return None, f"不支持的文件格式: {file_extension}"
                
            try:
                df_input = pd.read_excel(input_data, engine=engine)
            except Exception as e:
                # 如果第一次尝试失败，使用另一个引擎
                try:
                    alternate_engine = 'openpyxl' if engine == 'xlrd' else 'xlrd'
                    df_input = pd.read_excel(input_data, engine=alternate_engine)
                except Exception as nested_e:
                    return None, f"无法读取Excel文件: {str(e)}，备用引擎错误: {str(nested_e)}"
        else:
            # 已经是DataFrame
            df_input = input_data
        
        # 转换到期时间数据为DataFrame
        df_due_dates = pd.DataFrame(due_dates)
        
        # 确保订单ID列存在
        if '订单ID' not in df_input.columns:
            return None, "原始数据中没有'订单ID'列"
        
        if '订单ID' not in df_due_dates.columns:
            return None, "到期时间数据中没有'订单ID'列"
        
        # 合并数据
        # 首先创建一个结果DataFrame，复制输入数据
        df_result = df_input.copy()
        
        # 添加到期日期列，如果已存在则更新
        if '账单到期日期' not in df_result.columns:
            df_result['账单到期日期'] = None
        
        # 创建一个字典，用于存储每个订单ID对应的各期数到期日期
        # 格式: {订单ID: {期数: 到期日期, ...}, ...}
        order_periods = {}

        # 遍历所有的到期时间数据，按订单ID和期数整理
        for _, row in df_due_dates.iterrows():
            order_id = row['订单ID']
            period_raw = row.get('期数', 1)
            due_date = row['账单到期日期']
            
            # 确保期数是整数类型
            try:
                period = int(period_raw)
            except (ValueError, TypeError):
                period = 1  # 默认为第1期
            
            # 为每个订单创建期数字典
            if order_id not in order_periods:
                order_periods[order_id] = {}
            
            # 存储期数对应的到期日期
            order_periods[order_id][period] = due_date

        # 确定最大期数，用于创建表头
        max_period = 1
        for periods in order_periods.values():
            if periods and len(periods) > 0:
                current_max = max(periods.keys())
                if current_max > max_period:
                    max_period = current_max

        # 为每个期数创建新列
        for i in range(1, max_period + 1):
            column_name = f"第{i}期"
            df_result[column_name] = None

        # 填充各期数的到期日期
        for order_id, periods in order_periods.items():
            for period, due_date in periods.items():
                # 找到对应的行
                matching_rows = df_result['订单ID'] == order_id
                
                # 更新对应期数的列
                column_name = f"第{period}期"
                df_result.loc[matching_rows, column_name] = due_date
                
                # 同时更新账单到期日期列（使用第一期的值）
                if period == 1:
                    df_result.loc[matching_rows, '账单到期日期'] = due_date
        
        return df_result, None
    except Exception as e:
        return None, f"处理数据时出错: {str(e)}"


def format_output(data, template_path=None, output_path=None):
    """
    将处理后的数据格式化为所需的输出格式
    
    参数:
    - data: 处理后的DataFrame
    - template_path: 模板文件路径（可选）
    - output_path: 输出文件路径（可选）
    
    返回:
    - output_file: 输出文件路径
    - error: 错误信息（如有）
    """
    try:
        # 如果没有提供输出路径，创建一个默认的
        if not output_path:
            current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"处理后的订单数据_{current_time}.xlsx"
        
        # 查找所有"第X期"列
        period_columns = [col for col in data.columns if col.startswith('第') and col.endswith('期')]
        
        # 创建输出数据
        output_data = []
        
        # 处理每行数据
        for _, row in data.iterrows():
            # 获取必要字段并转换总租金为浮点数
            try:
                total_rent = float(row.get('总租金', 0))
            except (ValueError, TypeError):
                total_rent = 0.0
                
            # 获取需要的字段
            order_date = row.get('起租日期', '')
            end_date = row.get('结束日期', '')
            order_id = row.get('订单ID', '')
            customer_name = row.get('下单姓名', '')
            product_name = row.get('商品名称', '')
            total_periods = row.get('总期数', '')
            user_remark = row.get('用户备注', '')
            due_date = row.get('账单到期日期', '')
            
            # 转换日期为datetime对象
            start_date_obj = pd.to_datetime(order_date, errors='coerce', format='%Y-%m-%d %H:%M:%S')
            end_date_obj = pd.to_datetime(end_date, errors='coerce', format='%Y-%m-%d %H:%M:%S')
            
            # 处理可能的NaT值
            if pd.isna(start_date_obj):
                start_date_obj = pd.NaT
            if pd.isna(end_date_obj):
                end_date_obj = pd.NaT
            
            # 计算月份差异
            month_diff = 0
            if pd.notna(start_date_obj) and pd.notna(end_date_obj):
                # 提取日期组件
                start_year = start_date_obj.year
                start_month = start_date_obj.month
                end_year = end_date_obj.year
                end_month = end_date_obj.month
                
                # 计算完整月份差异
                month_diff = (end_year - start_year) * 12 + (end_month - start_month)
                
                # 如果结束日期的天数小于开始日期的天数，减去一个月
                if end_date_obj.day < start_date_obj.day:
                    month_diff -= 1
            
            # 根据月份差异确定还款周期、产品类型和店铺归属
            if month_diff >= 5:  # 大于等于5个月
                repayment_cycle = "月还"
                product_type = "租赁"
                shop_attribution = "林林租物"
                # 计算租赁单位数
                unit_count = total_rent / 13644 if total_rent > 0 else 0
                # 计算租赁总待收
                total_receivable = total_rent + (116 * unit_count) if total_rent > 0 else 0
            else:  # 小于5个月
                repayment_cycle = "天还"
                product_type = "电商"
                shop_attribution = "刚刚好物"
                # 计算电商单位数
                unit_count = total_rent / 11879.4 if total_rent > 0 else 0
                # 计算电商总待收
                total_receivable = total_rent + (119.6 * unit_count) if total_rent > 0 else 0
            
            # 准备输出行
            output_row = {
                '日期': format_date_to_slash(order_date),  # 起租日期
                '订单编号': order_id,  # 订单ID
                '客户姓名': customer_name,  # 下单姓名
                '型号': product_name,  # 商品名称
                '客户属性': '',  # 客户属性为空
                '用途': '自用',  # 默认填充"自用"
                '还款周期': repayment_cycle,  # 根据月份差异判断
                '产品': product_type,  # 根据月份差异判断
                '期数': f"{total_periods}期" if pd.notna(total_periods) else '',  # 总期数 + "期"
                '业务': '',  # 业务为空
                '总待收': round(total_receivable, 2) if total_receivable > 0 else '',  # 总待收计算
                '当前待收': '',  # 当前待收为空
                '备注': user_remark,  # 用户备注
                '每期还款金': '',  # 每期还款金为空
                '成本': '',  # 成本为空
                '店铺归属': shop_attribution,  # 根据产品类型设置店铺归属
                '台数': round(unit_count, 2) if unit_count > 0 else 0,  # 根据产品类型计算台数
                '账单到期日期': format_date_to_slash(due_date)  # 新增账单到期日期字段
            }
            
            # 添加各期到期日期，并格式化
            for col in period_columns:
                if col in row and pd.notna(row[col]):
                    output_row[col] = format_date_to_slash(row[col])
            
            output_data.append(output_row)
        
        # 转换输出数据为DataFrame
        df_output = pd.DataFrame(output_data)
        
        # 尝试按日期排序
        try:
            # 保存原始日期格式
            original_dates = df_output['日期'].copy()
            # 转换日期列为datetime以便正确排序
            df_output['日期'] = pd.to_datetime(df_output['日期'], errors='coerce', format='%Y/%m/%d')
            # 按日期排序
            df_output = df_output.sort_values(by=['日期'])
            # 转换回斜杠格式
            df_output['日期'] = df_output['日期'].apply(lambda x: format_date_to_slash(x) if pd.notna(x) else '')
        except Exception as e:
            # 如果排序失败，继续不排序
            print(f"按日期排序失败: {str(e)}")
            
        # 调整列顺序，确保账单到期日期相关的列排列在"备注"字段后面
        if '备注' in df_output.columns:
            # 获取所有列
            all_columns = df_output.columns.tolist()
            
            # 找到"备注"列的位置
            remark_index = all_columns.index('备注')
            
            # 将账单到期日期和所有第X期列从当前位置移除
            due_date_columns = ['账单到期日期'] + period_columns
            remaining_columns = [col for col in all_columns if col not in due_date_columns]
            
            # 重新构建列顺序，将这些列插入到"备注"列之后
            new_columns = remaining_columns[:remark_index+1] + due_date_columns + remaining_columns[remark_index+1:]
            
            # 重新排列列
            df_output = df_output[new_columns]
        
        # 如果提供了模板路径，尝试使用模板列结构
        if template_path and os.path.exists(template_path):
            try:
                # 确定模板引擎
                template_extension = os.path.splitext(template_path)[1].lower()
                engine = 'openpyxl' if template_extension == '.xlsx' else 'xlrd'
                
                # 读取模板结构
                df_template = pd.read_excel(template_path, engine=engine)
                template_columns = df_template.columns.tolist()
                
                # 确保所有模板列在输出数据中存在
                for col in template_columns:
                    if col not in df_output.columns:
                        df_output[col] = None
                
                # 保留所有第X期列
                for col in period_columns:
                    if col not in template_columns:
                        template_columns.append(col)
                
                # 重排列以匹配模板顺序，但保留第X期列
                all_columns = template_columns.copy()
                for col in df_output.columns:
                    if col not in all_columns:
                        all_columns.append(col)
                
                # 仅保留存在于df_output中的列
                final_columns = [col for col in all_columns if col in df_output.columns]
                df_output = df_output[final_columns]
            except Exception as e:
                print(f"使用模板结构失败: {str(e)}，使用默认列结构")
        
        # 保存到Excel
        df_output.to_excel(output_path, index=False)
        return output_path, None
    except Exception as e:
        return None, f"格式化输出时出错: {str(e)}"
