#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
租赁规则引擎
实现新租赁4+2和旧租赁6期的业务规则
"""

import logging
import pandas as pd
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from typing import Dict, List, Any, Optional, Tuple
from .pricing_config import PricingConfigManager

logger = logging.getLogger(__name__)

class LeaseRuleEngine:
    """租赁规则引擎"""
    
    def __init__(self):
        self.tolerance = 100  # 金额容差，默认100元
    
    def generate_payment_schedule(self, order: pd.Series, pricing_manager: PricingConfigManager) -> List[Dict[str, Any]]:
        """
        生成还款计划
        
        Args:
            order: 订单数据
            pricing_manager: 定价配置管理器
            
        Returns:
            还款计划列表
        """
        try:
            product_type = order.get('product_type', '').strip()
            periods = int(order.get('periods', 0))
            model_name = order.get('model', '')
            
            # 获取租金、首付、买断金
            rent, downpayment, buyout, estimation_source = self._get_payment_amounts(
                order, pricing_manager, product_type, model_name, periods
            )
            
            # 生成期次日期
            period_dates = self._generate_period_dates(order, periods)
            
            # 根据产品类型和期数生成还款计划
            if product_type == '租赁':
                if periods == 4:  # 新租赁4+2
                    schedules = self._generate_new_lease_schedule(
                        rent, downpayment, buyout, period_dates
                    )
                elif periods == 6:  # 旧租赁6期
                    schedules = self._generate_old_lease_schedule(
                        rent, buyout, period_dates
                    )
                else:
                    raise ValueError(f"不支持的租赁期数: {periods}")
            elif product_type == '电商':
                schedules = self._generate_ecommerce_schedule(rent, period_dates)
            else:
                raise ValueError(f"不支持的产品类型: {product_type}")
            
            # 添加估算来源信息
            for schedule in schedules:
                schedule['estimation_source'] = estimation_source
            
            return schedules
            
        except Exception as e:
            logger.error(f"生成还款计划失败: {e}")
            raise
    
    def _get_payment_amounts(self, order: pd.Series, pricing_manager: PricingConfigManager, 
                           product_type: str, model_name: str, periods: int) -> Tuple[float, float, float, Dict[str, str]]:
        """
        获取支付金额（租金、首付、买断金）
        
        Returns:
            (租金, 首付, 买断金, 估算来源)
        """
        estimation_source = {
            'rent': 'null',
            'downpayment': 'null', 
            'buyout': 'null'
        }
        
        # 1. 优先使用Excel中的数据
        rent = order.get('monthly_payment')
        contract_amount = order.get('contract_amount')
        
        if pd.notna(rent) and rent > 0:
            estimation_source['rent'] = 'excel'
        else:
            # 2. 尝试从定价配置获取
            pricing_config = pricing_manager.find_pricing_config(product_type, model_name, periods)
            if pricing_config:
                rent = pricing_config.get('rent_per_period', 0)
                estimation_source['rent'] = 'pricing'
                
                # 从定价配置获取首付和买断金
                downpayment = pricing_config.get('downpayment_default', 0)
                buyout = pricing_config.get('buyout_total', 0)
                estimation_source['downpayment'] = 'pricing'
                estimation_source['buyout'] = 'pricing'
                
                return float(rent), float(downpayment), float(buyout), estimation_source
            
            # 3. 使用合同金额反推
            elif pd.notna(contract_amount) and contract_amount > 0:
                rent, downpayment, buyout = self._reverse_calculate_amounts(
                    contract_amount, product_type, periods
                )
                estimation_source['rent'] = 'reverse'
                estimation_source['downpayment'] = 'reverse'
                estimation_source['buyout'] = 'reverse'
                
                return rent, downpayment, buyout, estimation_source
            else:
                raise ValueError("无法确定租金金额：Excel中无数据，定价配置未匹配，合同金额缺失")
        
        # 如果有租金但缺少首付和买断金，尝试从定价配置获取
        downpayment = 0
        buyout = 0
        
        pricing_config = pricing_manager.find_pricing_config(product_type, model_name, periods)
        if pricing_config:
            downpayment = pricing_config.get('downpayment_default', 0)
            buyout = pricing_config.get('buyout_total', 0)
            estimation_source['downpayment'] = 'pricing'
            estimation_source['buyout'] = 'pricing'
        
        return float(rent), float(downpayment), float(buyout), estimation_source
    
    def _reverse_calculate_amounts(self, contract_amount: float, product_type: str, periods: int) -> Tuple[float, float, float]:
        """
        根据合同金额反推租金、首付、买断金
        
        Args:
            contract_amount: 合同金额
            product_type: 产品类型
            periods: 期数
            
        Returns:
            (租金, 首付, 买断金)
        """
        if product_type == '电商':
            # 电商：合同金额 = 租金 × 期数
            rent = contract_amount / periods
            return rent, 0, 0
        
        elif product_type == '租赁':
            if periods == 6:  # 旧租赁
                # 假设：合同金额 = 首付 + 租金×5 + (租金-买断金)
                # 简化假设：首付=租金，买断金=租金*0.5
                # 合同金额 = 租金 + 租金×5 + (租金-租金×0.5) = 租金×6.5
                rent = contract_amount / 6.5
                downpayment = rent
                buyout = rent * 0.5
                return rent, downpayment, buyout
                
            elif periods == 4:  # 新租赁4+2
                # 假设：合同金额 = 首付 + 租金×4 + 买断金
                # 简化假设：首付=租金，买断金=租金*1.5
                # 合同金额 = 租金 + 租金×4 + 租金×1.5 = 租金×6.5
                rent = contract_amount / 6.5
                downpayment = rent
                buyout = rent * 1.5
                return rent, downpayment, buyout
        
        # 默认情况
        rent = contract_amount / periods
        return rent, 0, 0
    
    def _generate_period_dates(self, order: pd.Series, periods: int) -> List[datetime]:
        """
        生成期次日期
        
        Args:
            order: 订单数据
            periods: 期数
            
        Returns:
            期次日期列表
        """
        dates = []
        
        # 检查Excel中是否有期次日期
        excel_dates = []
        for i in range(1, 7):  # 最多6期
            period_date = order.get(f'period_{i}_date')
            if pd.notna(period_date):
                excel_dates.append(period_date)
            else:
                excel_dates.append(None)
        
        # 如果有足够的Excel日期，使用Excel日期
        valid_excel_dates = [d for d in excel_dates[:periods] if d is not None]
        if len(valid_excel_dates) >= periods:
            return valid_excel_dates[:periods]
        
        # 否则需要补齐日期
        base_date = None
        if valid_excel_dates:
            # 使用最后一个有效日期作为基准
            base_date = valid_excel_dates[-1]
            dates.extend(valid_excel_dates)
        else:
            # 使用订单日期作为基准
            order_date = order.get('order_date')
            if pd.notna(order_date):
                base_date = order_date
            else:
                base_date = datetime.now().date()
        
        # 补齐缺失的日期
        start_period = len(valid_excel_dates) + 1
        for i in range(start_period, periods + 1):
            if base_date:
                # 每期间隔1个月
                period_date = base_date + relativedelta(months=i-len(valid_excel_dates))
                dates.append(period_date)
        
        # 对于新租赁4+2，需要补齐第5、6期
        if periods == 4 and len(dates) == 4:
            # 第5期 = 第4期 + 1个月
            period_5_date = dates[3] + relativedelta(months=1)
            # 第6期 = 第4期 + 2个月  
            period_6_date = dates[3] + relativedelta(months=2)
            dates.extend([period_5_date, period_6_date])
        
        return dates
    
    def _generate_new_lease_schedule(self, rent: float, downpayment: float, buyout: float, 
                                   period_dates: List[datetime]) -> List[Dict[str, Any]]:
        """
        生成新租赁4+2还款计划
        
        Args:
            rent: 每期租金
            downpayment: 首付款
            buyout: 买断金总额
            period_dates: 期次日期列表
            
        Returns:
            还款计划列表
        """
        schedules = []
        
        # 1-4期：租金
        for i in range(4):
            schedule = {
                'period': i + 1,
                'due_date': period_dates[i] if i < len(period_dates) else None,
                'expected_amount': rent,
                'paid_amount': 0.0,
                'status': '未到期',
                'auto_inferred': i >= len(period_dates) - 2 if len(period_dates) >= 4 else False
            }
            schedules.append(schedule)
        
        # 第5期：买断金/2
        buyout_first = buyout / 2
        schedule_5 = {
            'period': 5,
            'due_date': period_dates[4] if len(period_dates) > 4 else None,
            'expected_amount': buyout_first,
            'paid_amount': 0.0,
            'status': '未到期',
            'auto_inferred': True
        }
        schedules.append(schedule_5)
        
        # 第6期：买断金/2 - 首付（如果<0，则为0，差额并入第5期）
        buyout_second = buyout / 2 - downpayment
        if buyout_second < 0:
            # 第6期为0，差额并入第5期
            schedules[4]['expected_amount'] = buyout - downpayment
            buyout_second = 0
        
        schedule_6 = {
            'period': 6,
            'due_date': period_dates[5] if len(period_dates) > 5 else None,
            'expected_amount': buyout_second,
            'paid_amount': 0.0,
            'status': '未到期',
            'auto_inferred': True
        }
        schedules.append(schedule_6)
        
        return schedules
    
    def _generate_old_lease_schedule(self, rent: float, buyout: float, 
                                   period_dates: List[datetime]) -> List[Dict[str, Any]]:
        """
        生成旧租赁6期还款计划
        
        Args:
            rent: 每期租金
            buyout: 买断金
            period_dates: 期次日期列表
            
        Returns:
            还款计划列表
        """
        schedules = []
        
        # 1-5期：租金
        for i in range(5):
            schedule = {
                'period': i + 1,
                'due_date': period_dates[i] if i < len(period_dates) else None,
                'expected_amount': rent,
                'paid_amount': 0.0,
                'status': '未到期',
                'auto_inferred': False
            }
            schedules.append(schedule)
        
        # 第6期：租金 - 买断金（不足0按0）
        final_amount = max(rent - buyout, 0)
        schedule_6 = {
            'period': 6,
            'due_date': period_dates[5] if len(period_dates) > 5 else None,
            'expected_amount': final_amount,
            'paid_amount': 0.0,
            'status': '未到期',
            'auto_inferred': False
        }
        schedules.append(schedule_6)
        
        return schedules
    
    def _generate_ecommerce_schedule(self, rent: float, period_dates: List[datetime]) -> List[Dict[str, Any]]:
        """
        生成电商还款计划
        
        Args:
            rent: 每期租金
            period_dates: 期次日期列表
            
        Returns:
            还款计划列表
        """
        schedules = []
        
        # 各期均为租金
        for i, date in enumerate(period_dates):
            schedule = {
                'period': i + 1,
                'due_date': date,
                'expected_amount': rent,
                'paid_amount': 0.0,
                'status': '未到期',
                'auto_inferred': False
            }
            schedules.append(schedule)
        
        return schedules
