# Bill-Helper 项目文档

面向财务与运营的数据处理与自动化工具集，包含 Web 账单助手、聚合二维码流水匹配器以及多文件填充等 CLI 工具，助力提高数据处理效率与准确性。

## 1. 项目结构与模块说明

```
Bill-Helper/
├── bill-helper/                  # Web 账单助手（Flask，端口 5001）
│   ├── app.py                    # 入口（运行后访问 http://localhost:5001）
│   ├── config.py                 # 配置（uploads/、processed/、扩展名等）
│   ├── modules/                  # 功能模块
│   │   ├── auth/
│   │   ├── crawler/
│   │   ├── processor/
│   │   └── exporter/
│   └── templates/                # 页面模板
├── qr_flow_matcher.py            # 聚合二维码流水匹配器（CLI）
├── enhanced_qr_matcher.py        # 增强匹配器（CLI）
├── qr_data_merger.py             # 数据合并（CLI）
├── qr_transaction_extractor.py   # 交易提取（CLI）
├── multi_file_filler_correct.py  # 多文件填充（CLI）
├── requirements.txt              # 根部依赖（部分 CLI 运行所需）
├── bill-helper/requirements.txt  # Web 账单助手依赖清单
└── README.md
```

配置参考：`bill-helper/config.py` 使用 `uploads/` 与 `processed/` 作为默认目录，允许扩展名为 `xls/xlsx`。本地默认账号仅用于开发调试，请勿在生产中使用。

## 2. 环境准备与运行

以下步骤以 Windows PowerShell 为例（推荐在项目根目录执行）：

```powershell
# 1) 创建并激活虚拟环境（Windows PowerShell）
python -m venv .venv
. .venv/Scripts/Activate.ps1

# 2) 安装依赖（Web 应用）
pip install -r bill-helper/requirements.txt

# 3) 安装浏览器（用于爬虫/自动化）
python -m playwright install
```

运行 Web 应用：

```powershell
python bill-helper/app.py
# 浏览器访问：http://localhost:5001
```

运行 CLI 工具（示例）：

```powershell
# 聚合二维码流水匹配器（需准备 TZ.xlsx 与 LS.xlsx 放在仓库根目录）
python qr_flow_matcher.py

# 多文件内容填充工具（目标文件一般为 TTXW.xlsx）
python multi_file_filler_correct.py
```

可选：代码质量与格式化（如已安装）

```powershell
ruff check .
isort .
```

## 3. 功能概览

- 账单助手（Web）：
  - Excel 上传校验（支持含“订单ID”列的 `xls/xlsx`）
  - 平台登录与账单到期时间抓取（Playwright）
  - 数据合并、格式化与 Excel 导出（`processed/` 下生成结果）
  - 异步处理与进度查询，端口 `5001`
- 聚合二维码流水匹配器（CLI）：
  - 基于时间/金额/备注/客户唯一性等多维度策略评分匹配
  - 生成综合匹配报告与结果 Excel
  - 运行前需在仓库根目录准备 `TZ.xlsx` + `LS.xlsx`
- 多文件内容填充工具（CLI）：
  - 批量读取多个源 Excel，填充到目标工作簿（如 `TTXW.xlsx`）
  - 支持日期格式转换、记录补充与排序

更多细节可参考 `使用指南.md`。

## 4. 代码规范与命名约定

- Python 3.8+，遵循 PEP 8，4 空格缩进，命名：函数/变量 `snake_case`、类 `PascalCase`、模块文件 `snake_case.py`。
- 导入：分组并排序（建议使用 `isort`），保持整洁无未使用导入（`ruff` 可检查）。
- 适当添加类型注解；必要处可使用中文注释/文档说明业务语义。

## 5. 测试建议

- 测试框架推荐 `pytest`，测试位于 `tests/` 下，文件命名为 `test_*.py`。
- 优先覆盖：
  - 处理器/导出器的关键逻辑（纯函数优先）
  - Web API 路由的冒烟用例（文件上传/处理/导出）
- 运行示例：

```powershell
pip install pytest
pytest -q
```

## 6. 提交与 PR 规范

- 提交信息：使用 Conventional Commits，例如 `feat: ...`、`fix: ...`、`docs: ...`、`refactor: ...`、`test: ...`。
- PR 必须包含：
  - 变更摘要、动机与范围
  - 复现/验证步骤（命令、样例 Excel 输入/输出）
  - Web 变更附截图或返回 JSON
  - 关联 issue/参考链接（如有）

## 7. 安全与配置建议

- 勿提交真实账号、密码、Cookie 或私有数据；`config.py` 中默认账号仅用于本地开发演示。
- 生产/测试环境敏感信息请使用环境变量或外部安全存储；日志中避免输出敏感字段。
- 大文件与隐私数据不要提交到仓库；示例数据尽量精简。
- Playwright 自动化易受页面变更影响，建议增加错误兜底与重试逻辑。

## 8. 常见问题（FAQ）

- 无法启动 Web：
  - 检查 `5001` 端口占用；尝试 `http://127.0.0.1:5001`
  - 已安装浏览器内核：`python -m playwright install`
- CLI 找不到输入文件：
  - 确认 `TZ.xlsx`、`LS.xlsx`（或 `TTXW.xlsx`）位于仓库根目录且未被占用
- 依赖缺失：
  - Web 请安装 `bill-helper/requirements.txt`；CLI 最低需 `requirements.txt`

---

最后更新：2025-09-11（对齐仓库规范与指令）
