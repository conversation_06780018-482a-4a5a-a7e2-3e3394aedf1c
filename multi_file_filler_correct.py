#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多文件内容填充工具 - 正确映射版本
修复字段映射错误，正确处理10个还款期
"""

import os
import sys
import pandas as pd
from datetime import datetime
from typing import List
import glob
from openpyxl import load_workbook


class CorrectMultiFileFiller:
    def __init__(self, target_file: str = "TTXW.xlsx"):
        self.target_file = target_file
        self.target_sheet = "订单管理"
        
    def read_source_file(self, file_path: str) -> pd.DataFrame:
        """读取源文件数据"""
        try:
            print(f"正在读取源文件: {file_path}")
            df = pd.read_excel(file_path, engine='openpyxl')
            print(f"成功读取 {len(df)} 行数据")
            return df
        except Exception as e:
            print(f"读取源文件失败: {e}")
            return None
    
    def read_existing_order_data(self) -> List[List]:
        """读取现有订单管理表中的数据"""
        try:
            print(f"正在读取现有订单管理数据: {self.target_file}")
            
            # 使用openpyxl读取现有数据
            wb = load_workbook(self.target_file)
            
            if self.target_sheet not in wb.sheetnames:
                print(f"错误：工作簿中不存在'{self.target_sheet}'工作表")
                return []
            
            ws = wb[self.target_sheet]
            
            # 读取数据（跳过前4行表头）
            order_data = []
            for row in range(5, ws.max_row + 1):
                # 检查A列是否有数据（日期）
                if ws.cell(row=row, column=1).value:
                    row_data = []
                    # 读取A到AB列（28列）
                    for col in range(1, 29):
                        cell_value = ws.cell(row=row, column=col).value
                        row_data.append(cell_value if cell_value is not None else "")
                    order_data.append(row_data)
            
            wb.close()
            print(f"成功读取现有订单数据 {len(order_data)} 行")
            return order_data
            
        except Exception as e:
            print(f"读取现有订单数据失败: {e}")
            return []
    
    def generate_cash_flow_from_existing_orders(self) -> bool:
        """根据现有订单数据生成资金流水记录"""
        try:
            print("📊 开始根据现有订单数据生成资金流水记录...")
            
            # 读取现有订单数据
            order_data = self.read_existing_order_data()
            if not order_data:
                print("❌ 没有找到有效的订单数据")
                return False
            
            print(f"✅ 找到 {len(order_data)} 条订单数据")
            
            # 询问用户是否确认生成
            print("\n订单数据预览（前3条）：")
            for i, row in enumerate(order_data[:3]):
                print(f"  订单{i+1}: {row[0]} | {row[1]} | {row[2]} | 总待收: {row[10]}")
            
            if len(order_data) > 3:
                print(f"  ... 还有 {len(order_data) - 3} 条订单")
            
            print(f"\n💡 每个订单将生成4条资金流水记录：")
            print("   1. 放款记录（-10500元×台数）")
            print("   2. 供应商利润记录（-100元×台数）")
            print("   3. 首付款记录（租赁：610元×台数 | 电商：119.6元×台数）")
            print("   4. 服务费记录（租赁：增值服务费590元×台数 | 电商：延保服务费1799.85元×台数）")
            
            confirm = input(
                f"\n是否确认为这 {len(order_data)} 条订单生成 {len(order_data) * 4} 条资金流水记录？(y/n): "
            ).lower()
            if confirm not in ['y', 'yes', '是']:
                print("❌ 用户取消操作")
                return False
            
            # 生成资金流水记录
            result = self.fill_cash_flow_sheet(order_data)
            if result:
                print("✅ 资金流水记录生成完成！")
                return True
            else:
                print("❌ 资金流水记录生成失败！")
                return False
            
        except Exception as e:
            print(f"生成资金流水记录失败: {e}")
            return False
    
    def convert_date_to_datetime(self, date_str):
        """转换日期格式为datetime对象，供Excel识别"""
        try:
            if pd.isna(date_str) or date_str == "":
                return None
            
            # 如果是字符串格式的日期
            if isinstance(date_str, str):
                if '/' in date_str:
                    year, month, day = date_str.split('/')
                    # 转换为datetime对象
                    return datetime(int(year), int(month), int(day))
                else:
                    return date_str
            
            # 如果已经是datetime对象
            if isinstance(date_str, datetime):
                return date_str
            
            return str(date_str)
        except Exception as e:
            print(f"日期格式转换失败: {date_str}, 错误: {e}")
            return str(date_str)
    
    def process_source_data(self, df: pd.DataFrame) -> List[List]:
        """处理源文件数据，转换为目标格式"""
        processed_data = []
        
        for _, row in df.iterrows():
            # 创建目标行数据，包含A到AB列（共28列）
            target_row = [''] * 28
            
            # A-M列：基础字段直接映射（索引0-12）
            for i in range(13):
                if i < len(row):
                    value = row.iloc[i]
                    if i == 0:  # A列是日期，需要转换为datetime对象
                        value = self.convert_date_to_datetime(value)
                    target_row[i] = value if not pd.isna(value) else ""
            
            # N-W列：还款详情（目标文件有10期，源文件只有6期）
            # 源文件O-T列（索引14-19）→ 目标文件N-S列（索引13-18）
            for i in range(6):  # 只映射前6期
                src_idx = 14 + i  # 源文件O-T列
                target_idx = 13 + i  # 目标文件N-S列
                
                if src_idx < len(row):
                    value = row.iloc[src_idx]
                    if not pd.isna(value):
                        # 转换为datetime对象
                        converted_date = self.convert_date_to_datetime(value)
                        target_row[target_idx] = converted_date
            
            # T-W列（索引19-22）：目标文件的第7-10期，保持为空
            # （因为源文件只有6期数据）
            
            # X列：每期还款金（源文件U列→目标文件X列）
            if 20 < len(row):  # 源文件U列（索引20）
                value = row.iloc[20]
                target_row[23] = value if not pd.isna(value) else ""  # 目标X列（索引23）
            
            # Y列：成本（源文件V列→目标文件Y列）
            if 21 < len(row):  # 源文件V列（索引21）
                value = row.iloc[21]
                target_row[24] = value if not pd.isna(value) else ""  # 目标Y列（索引24）
            
            # Z列：店铺归属（源文件W列→目标文件Z列）
            if 22 < len(row):  # 源文件W列（索引22）
                value = row.iloc[22]
                target_row[25] = value if not pd.isna(value) else ""  # 目标Z列（索引25）
            
            # AA列：台数（源文件X列→目标文件AA列）
            if 23 < len(row):  # 源文件X列（索引23）
                value = row.iloc[23]
                target_row[26] = value if not pd.isna(value) else ""  # 目标AA列（索引26）
            
            # AB列：还款状态说明（设置为"待还款"）
            if 13 < len(row) and not pd.isna(row.iloc[13]):  # 如果有账单到期日期
                target_row[27] = "待还款"  # 目标AB列（索引27）
            
            processed_data.append(target_row)
        
        return processed_data
    
    def sort_by_date(self, data: List[List]) -> List[List]:
        """按日期排序数据"""
        try:
            def date_sort_key(row):
                date_value = row[0]  # A列是日期
                if not date_value:
                    return datetime.min
                
                try:
                    # 如果已经是datetime对象，直接返回
                    if isinstance(date_value, datetime):
                        return date_value
                    
                    # 如果是字符串格式的日期，解析它
                    if isinstance(date_value, str) and '-' in date_value:
                        parts = date_value.split('-')
                        if len(parts) == 3:
                            year, month, day = parts
                            return datetime(int(year), int(month), int(day))
                    
                    return datetime.min
                except Exception:
                    return datetime.min
            
            sorted_data = sorted(data, key=date_sort_key)
            print(f"按日期排序完成，共 {len(sorted_data)} 行数据")
            return sorted_data
        except Exception as e:
            print(f"日期排序失败: {e}")
            return data
    
    def write_to_target_file_safe(self, new_data: List[List]) -> bool:
        """使用openpyxl安全地写入数据到目标文件"""
        try:
            # 加载工作簿
            print(f"正在加载目标文件: {self.target_file}")
            wb = load_workbook(self.target_file)
            
            if self.target_sheet not in wb.sheetnames:
                print(f"错误：工作簿中不存在'{self.target_sheet}'工作表")
                return False
            
            ws = wb[self.target_sheet]
            
            # 从第5行开始写入数据
            start_row = 5
            current_row = start_row
            
            # 清空从第5行开始的所有数据（保留表头）
            print("正在清空现有数据...")
            max_row_to_clear = max(ws.max_row, start_row + len(new_data) + 10)
            for row in range(start_row, max_row_to_clear + 1):
                for col in range(1, 29):  # A到AB列
                    ws.cell(row=row, column=col).value = None
            
            # 写入新数据
            print(f"正在写入 {len(new_data)} 行新数据...")
            for row_data in new_data:
                for col_idx, value in enumerate(row_data):
                    if col_idx < 28:  # 确保只写入A到AB列
                        ws.cell(row=current_row, column=col_idx + 1).value = value
                current_row += 1
            
            # 保存工作簿
            print("正在保存文件...")
            wb.save(self.target_file)
            wb.close()
            
            print(f"成功写入 {len(new_data)} 行数据到目标文件")
            return True
            
        except Exception as e:
            print(f"写入目标文件失败: {e}")
            return False
    
    def process_multiple_files(self, source_files: List[str]) -> bool:
        """处理多个源文件"""
        all_data = []
        
        for file_path in source_files:
            if not os.path.exists(file_path):
                print(f"源文件不存在: {file_path}")
                continue
            
            # 读取源文件
            df = self.read_source_file(file_path)
            if df is None:
                continue
            
            # 处理数据
            processed_data = self.process_source_data(df)
            all_data.extend(processed_data)
            print(f"已处理文件: {file_path}, 获得 {len(processed_data)} 行数据")
        
        if not all_data:
            print("没有有效数据可处理")
            return False
        
        # 按日期排序
        sorted_data = self.sort_by_date(all_data)
        
        # 使用安全方法写入目标文件
        result = self.write_to_target_file_safe(sorted_data)
        
        # 如果订单管理填充成功，再填充资金流水账
        if result:
            print("\n开始填充资金流水账分表...")
            cash_flow_result = self.fill_cash_flow_sheet(sorted_data)
            if cash_flow_result:
                print("✅ 资金流水账分表填充完成！")
            else:
                print("❌ 资金流水账分表填充失败！")
        
        return result
    
    def fill_cash_flow_sheet(self, order_data: List[List]) -> bool:
        """根据订单管理数据填充资金流水账分表"""
        try:
            # 加载工作簿
            wb = load_workbook(self.target_file)
            
            if "资金流水账" not in wb.sheetnames:
                print("错误：工作簿中不存在'资金流水账'工作表")
                return False
            
            ws = wb["资金流水账"]
            
            # 找到插入位置（在分隔符行之前插入）
            # 从第5行开始查找，找到分隔符行或计算判断行
            insert_row = 5
            
            # 查找分隔符行（K列为"-"）或计算判断行（A列为"计算判断行"）
            for row in range(5, ws.max_row + 1):
                try:
                    a_cell = ws.cell(row=row, column=1).value
                    k_cell = ws.cell(row=row, column=11).value
                    
                    # 如果找到分隔符行或计算判断行，在此之前插入
                    if (k_cell == "-" or a_cell == "计算判断行"):
                        insert_row = row
                        break
                        
                    # 如果找到空行，也可以插入
                    if (a_cell is None or a_cell == "") and row > 8:
                        insert_row = row
                        break
                        
                except Exception:
                    continue
            
            print(f"找到插入位置：第{insert_row}行")
            
            # 为每行订单数据生成两条资金流水记录
            cash_flow_data = []
            
            for order_row in order_data:
                # 获取基础信息
                date_val = order_row[0]           # A列：日期
                order_no = order_row[1]           # B列：订单编号
                customer_name = order_row[2]      # C列：客户姓名
                model = order_row[3]              # D列：型号
                customer_attr = order_row[4]      # E列：客户属性
                purpose = order_row[5]            # F列：用途
                repay_cycle = order_row[6]        # G列：还款周期
                product = order_row[7]            # H列：产品
                quantity = order_row[26]          # AA列：台数
                
                # 处理台数，确保是数值
                try:
                    qty = float(quantity) if quantity else 1
                except (ValueError, TypeError):
                    qty = 1
                
                # 第一条记录：放款
                loan_record = [
                    date_val,           # A列：日期
                    order_no,           # B列：订单编号
                    customer_name,      # C列：客户姓名
                    model,              # D列：型号
                    customer_attr,      # E列：客户属性
                    purpose,            # F列：用途
                    repay_cycle,        # G列：还款周期
                    product,            # H列：产品
                    -(10500 * qty),     # I列：交易金额（10500元*台数）
                    "",                 # J列：归属期数
                    "放款",             # K列：交易类型
                    "放款卡",           # L列：资金流向
                    "",                 # M列：交易订单号
                    "",                 # N列：可用余额
                    "",                 # O列：待提现余额
                    ""                  # P列：备注
                ]
                
                # 第二条记录：供应商利润
                profit_record = [
                    date_val,           # A列：日期
                    order_no,           # B列：订单编号
                    customer_name,      # C列：客户姓名
                    model,              # D列：型号
                    customer_attr,      # E列：客户属性
                    purpose,            # F列：用途
                    repay_cycle,        # G列：还款周期
                    product,            # H列：产品
                    -(100 * qty),       # I列：交易金额（100*台数）
                    "",                 # J列：归属期数
                    "供应商利润",       # K列：交易类型
                    "卢垫",             # L列：资金流向
                    "",                 # M列：交易订单号
                    "",                 # N列：可用余额
                    "",                 # O列：待提现余额
                    ""                  # P列：备注
                ]
                
                # 根据产品类型确定首付款和服务费
                if product == "租赁":
                    # 租赁产品
                    down_payment_amount = 610 * qty      # 首付款
                    service_fee_amount = 590 * qty       # 增值服务费
                    service_fee_type = "增值服务费"
                elif product == "电商":
                    # 电商产品
                    down_payment_amount = 119.6 * qty    # 首付款
                    service_fee_amount = 1799.85 * qty   # 延保服务费
                    service_fee_type = "延保服务费"
                else:
                    # 其他产品类型，使用默认值（租赁规则）
                    down_payment_amount = 610 * qty
                    service_fee_amount = 590 * qty
                    service_fee_type = "增值服务费"
                
                # 第三条记录：首付款
                down_payment_record = [
                    date_val,           # A列：日期
                    order_no,           # B列：订单编号
                    customer_name,      # C列：客户姓名
                    model,              # D列：型号
                    customer_attr,      # E列：客户属性
                    purpose,            # F列：用途
                    repay_cycle,        # G列：还款周期
                    product,            # H列：产品
                    down_payment_amount, # I列：交易金额（首付款）
                    "",                 # J列：归属期数
                    "首付款",           # K列：交易类型
                    "放款卡",           # L列：资金流向
                    "",                 # M列：交易订单号
                    "",                 # N列：可用余额
                    "",                 # O列：待提现余额
                    ""                  # P列：备注
                ]
                
                # 第四条记录：增值服务费或延保服务费
                service_fee_record = [
                    date_val,           # A列：日期
                    order_no,           # B列：订单编号
                    customer_name,      # C列：客户姓名
                    model,              # D列：型号
                    customer_attr,      # E列：客户属性
                    purpose,            # F列：用途
                    repay_cycle,        # G列：还款周期
                    product,            # H列：产品
                    service_fee_amount, # I列：交易金额（服务费）
                    "",                 # J列：归属期数
                    service_fee_type,   # K列：交易类型
                    "放款卡",           # L列：资金流向
                    "",                 # M列：交易订单号
                    "",                 # N列：可用余额
                    "",                 # O列：待提现余额
                    ""                  # P列：备注
                ]
                
                # 添加所有4条记录
                cash_flow_data.append(loan_record)
                cash_flow_data.append(profit_record)
                cash_flow_data.append(down_payment_record)
                cash_flow_data.append(service_fee_record)
            
            # 写入数据
            print(f"正在写入 {len(cash_flow_data)} 条资金流水记录...")
            current_row = insert_row
            for record in cash_flow_data:
                for col_idx, value in enumerate(record):
                    if col_idx < 16:  # 确保只写入A到P列
                        ws.cell(row=current_row, column=col_idx + 1).value = value
                current_row += 1
            
            # 保存工作簿
            print("正在保存资金流水账...")
            wb.save(self.target_file)
            wb.close()
            
            print(f"成功写入 {len(cash_flow_data)} 条资金流水记录")
            return True
            
        except Exception as e:
            print(f"填充资金流水账失败: {e}")
            return False
    
    def preview_data(self, source_files: List[str], max_rows: int = 3):
        """预览数据，不写入文件"""
        all_data = []
        
        for file_path in source_files:
            if not os.path.exists(file_path):
                print(f"源文件不存在: {file_path}")
                continue
            
            df = self.read_source_file(file_path)
            if df is None:
                continue
            
            processed_data = self.process_source_data(df)
            all_data.extend(processed_data)
        
        if not all_data:
            print("没有有效数据可预览")
            return
        
        # 按日期排序
        sorted_data = self.sort_by_date(all_data)
        
        # 显示预览
        print(f"\n数据预览（前{max_rows}行）：")
        print("=" * 100)
        
        # 定义列名对应关系
        column_names = [
            "A-日期", "B-订单编号", "C-客户姓名", "D-型号", "E-客户属性", "F-用途", 
            "G-还款周期", "H-产品", "I-期数", "J-业务", "K-总待收", "L-当前待收", "M-备注",
            "N-第1期", "O-第2期", "P-第3期", "Q-第4期", "R-第5期", "S-第6期",
            "T-第7期", "U-第8期", "V-第9期", "W-第10期",
            "X-每期还款金", "Y-成本", "Z-店铺归属", "AA-台数", "AB-还款状态"
        ]
        
        for i, row in enumerate(sorted_data[:max_rows]):
            print(f"\n第{i+1}行数据：")
            for j, col_name in enumerate(column_names):
                if j < len(row) and row[j]:
                    print(f"  {col_name}: {row[j]}")
            print("-" * 80)


class CorrectInteractiveCLI:
    def __init__(self):
        self.filler = CorrectMultiFileFiller()
        self.selected_files = []
        
    def print_header(self):
        """打印程序头部信息"""
        print("\n" + "="*70)
        print("        多文件内容填充工具（正确映射版）")
        print("      修复字段映射错误，正确处理10个还款期")
        print("="*70)
        
    def print_menu(self):
        """打印主菜单"""
        print("\n主菜单：")
        print("1. 选择源文件")
        print("2. 预览数据")
        print("3. 执行填充")
        print("4. 查看已选择文件")
        print("5. 设置目标文件")
        print("6. 根据现有订单生成资金流水")
        print("7. 退出")
        print("-" * 40)
        
    def find_excel_files(self):
        """查找当前目录下的Excel文件"""
        excel_files = []
        patterns = ['*.xlsx', '*.xls']
        
        for pattern in patterns:
            files = glob.glob(pattern)
            excel_files.extend(files)
        
        # 过滤掉目标文件
        excel_files = [f for f in excel_files if f != self.filler.target_file]
        return excel_files
        
    def select_files(self):
        """选择源文件"""
        excel_files = self.find_excel_files()
        
        if not excel_files:
            print("当前目录下没有找到Excel文件")
            return
        
        print("\n找到以下Excel文件：")
        for i, file in enumerate(excel_files, 1):
            print(f"{i}. {file}")
        
        print("\n选择文件（输入序号，多个文件用逗号分隔，或输入'all'选择全部）：")
        
        while True:
            try:
                choice = input("请输入: ").strip()
                
                if choice.lower() == 'all':
                    self.selected_files = excel_files[:]
                    print(f"已选择全部 {len(self.selected_files)} 个文件")
                    break
                
                if choice.lower() == 'quit' or choice.lower() == 'q':
                    break
                
                # 解析选择的文件序号
                selected_indices = []
                for part in choice.split(','):
                    part = part.strip()
                    if part.isdigit():
                        idx = int(part) - 1
                        if 0 <= idx < len(excel_files):
                            selected_indices.append(idx)
                        else:
                            print(f"无效序号: {part}")
                    else:
                        print(f"无效输入: {part}")
                
                if selected_indices:
                    self.selected_files = [excel_files[i] for i in selected_indices]
                    print(f"已选择 {len(self.selected_files)} 个文件:")
                    for file in self.selected_files:
                        print(f"  - {file}")
                    break
                else:
                    print("请输入有效的文件序号")
                    
            except KeyboardInterrupt:
                print("\n操作已取消")
                break
            except Exception as e:
                print(f"输入错误: {e}")
    
    def preview_data(self):
        """预览数据"""
        if not self.selected_files:
            print("请先选择源文件")
            return
        
        print(f"\n预览已选择的 {len(self.selected_files)} 个文件的数据...")
        
        try:
            rows = int(input("请输入要预览的行数（默认3行）: ") or "3")
        except ValueError:
            rows = 3
        
        self.filler.preview_data(self.selected_files, rows)
    
    def execute_fill(self):
        """执行填充"""
        if not self.selected_files:
            print("请先选择源文件")
            return
        
        print(f"\n准备填充数据...")
        print(f"源文件: {self.selected_files}")
        print(f"目标文件: {self.filler.target_file}")
        
        # 警告提示
        print("\n⚠️  警告：此操作将清空目标文件中第5行及以后的所有数据！")
        print("建议先备份目标文件！")
        
        confirm = input("\n确认要执行数据填充吗？(y/N): ").strip().lower()
        if confirm != 'y':
            print("操作已取消")
            return
        
        print("开始执行填充...")
        success = self.filler.process_multiple_files(self.selected_files)
        
        if success:
            print("\n✅ 数据填充完成！")
        else:
            print("\n❌ 数据填充失败！")
    
    def show_selected_files(self):
        """显示已选择的文件"""
        if not self.selected_files:
            print("尚未选择任何文件")
        else:
            print(f"\n已选择 {len(self.selected_files)} 个文件:")
            for i, file in enumerate(self.selected_files, 1):
                print(f"{i}. {file}")
    
    def set_target_file(self):
        """设置目标文件"""
        current_target = self.filler.target_file
        print(f"\n当前目标文件: {current_target}")
        
        new_target = input("请输入新的目标文件名（直接回车保持不变）: ").strip()
        
        if new_target:
            if not new_target.endswith('.xlsx'):
                new_target += '.xlsx'
            
            if os.path.exists(new_target):
                self.filler.target_file = new_target
                print(f"目标文件已更改为: {new_target}")
            else:
                print(f"警告: 目标文件 {new_target} 不存在")
                confirm = input("是否仍要设置为目标文件？(y/N): ").strip().lower()
                if confirm == 'y':
                    self.filler.target_file = new_target
                    print(f"目标文件已设置为: {new_target}")
    
    def generate_cash_flow_from_orders(self):
        """根据现有订单生成资金流水"""
        print("\n🏦 根据现有订单生成资金流水记录")
        print("=" * 50)
        
        # 检查目标文件是否存在
        if not os.path.exists(self.filler.target_file):
            print(f"❌ 目标文件 {self.filler.target_file} 不存在")
            return
        
        # 执行生成
        success = self.filler.generate_cash_flow_from_existing_orders()
        
        if success:
            print("\n✅ 资金流水生成成功！")
        else:
            print("\n❌ 资金流水生成失败！")
    
    def run(self):
        """运行交互式界面"""
        self.print_header()
        
        while True:
            self.print_menu()
            
            try:
                choice = input("请选择操作 (1-7): ").strip()
                
                if choice == '1':
                    self.select_files()
                elif choice == '2':
                    self.preview_data()
                elif choice == '3':
                    self.execute_fill()
                elif choice == '4':
                    self.show_selected_files()
                elif choice == '5':
                    self.set_target_file()
                elif choice == '6':
                    self.generate_cash_flow_from_orders()
                elif choice == '7':
                    print("感谢使用！再见！")
                    break
                else:
                    print("无效选择，请输入1-7之间的数字")
                    
            except KeyboardInterrupt:
                print("\n\n程序已中断，再见！")
                break
            except Exception as e:
                print(f"发生错误: {e}")


def main():
    """主函数"""
    try:
        cli = CorrectInteractiveCLI()
        cli.run()
    except Exception as e:
        print(f"程序运行出错: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())