#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ETL处理器主模块
负责协调整个ETL流程：读取、转换、加载
"""

import logging
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, Tuple
from .excel_reader import ExcelReader
from .lease_rules import LeaseRuleEngine
from .pricing_config import PricingConfigManager
from ..database.connection import get_db_session
from ..database.models import ImportReport

logger = logging.getLogger(__name__)

class ETLProcessor:
    """ETL处理器"""
    
    def __init__(self, file_path: str):
        """
        初始化ETL处理器
        
        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        self.run_id = self._generate_run_id()
        self.excel_reader = ExcelReader(file_path)
        self.lease_engine = LeaseRuleEngine()
        self.pricing_manager = PricingConfigManager()
        self.report_data = {
            'run_id': self.run_id,
            'started_at': datetime.utcnow().isoformat(),
            'finished_at': None,
            'summary': {
                'total_orders': 0,
                'total_transactions': 0,
                'total_customers': 0,
                'new_lease_orders': 0,
                'old_lease_orders': 0,
                'ecommerce_orders': 0,
                'used_pricing_count': 0,
                'used_reverse_calc_count': 0,
                'contract_check_pass': 0,
                'contract_check_fail': 0
            },
            'issues': [],
            'orders': []
        }
    
    def _generate_run_id(self) -> str:
        """生成运行ID"""
        timestamp = datetime.utcnow().strftime('%Y%m%dT%H%M%SZ')
        unique_id = str(uuid.uuid4())[:8]
        return f"{timestamp}_{unique_id}"
    
    def process(self, dry_run: bool = False) -> Dict[str, Any]:
        """
        执行完整的ETL流程
        
        Args:
            dry_run: 是否为试运行（不写入数据库）
            
        Returns:
            处理结果报告
        """
        try:
            logger.info(f"开始ETL处理，运行ID: {self.run_id}")
            
            # 1. 读取Excel数据
            self._read_excel_data()
            
            # 2. 加载配置
            self._load_configurations()
            
            # 3. 数据验证
            self._validate_data()
            
            # 4. 数据转换和处理
            self._transform_data()
            
            # 5. 生成还款计划
            self._generate_payment_schedules()
            
            # 6. 计算账单状态
            self._calculate_payment_status()
            
            # 7. 合同金额校验
            self._validate_contracts()
            
            # 8. 写入数据库（如果不是试运行）
            if not dry_run:
                self._load_to_database()
            
            # 9. 生成报告
            self._finalize_report()
            
            logger.info(f"ETL处理完成，运行ID: {self.run_id}")
            return self.report_data
            
        except Exception as e:
            logger.error(f"ETL处理失败: {e}")
            self.report_data['error'] = str(e)
            self.report_data['finished_at'] = datetime.utcnow().isoformat()
            raise
    
    def _read_excel_data(self):
        """读取Excel数据"""
        logger.info("正在读取Excel数据...")
        
        # 读取所有工作表
        self.excel_reader.read_all_sheets()
        
        # 验证数据
        is_valid, error_msg = self.excel_reader.validate_required_data()
        if not is_valid:
            raise ValueError(f"数据验证失败: {error_msg}")
        
        # 获取各表数据
        self.order_data = self.excel_reader.get_order_management_data()
        self.transaction_data = self.excel_reader.get_transaction_data()
        self.customer_data = self.excel_reader.get_customer_supplement_data()
        
        logger.info(f"数据读取完成 - 订单: {len(self.order_data)}, 交易: {len(self.transaction_data)}, 客户: {len(self.customer_data) if self.customer_data is not None else 0}")
    
    def _load_configurations(self):
        """加载配置"""
        logger.info("正在加载配置...")
        
        # 加载定价配置
        self.pricing_manager.load_pricing_config()
        
        logger.info("配置加载完成")
    
    def _validate_data(self):
        """数据验证"""
        logger.info("正在进行数据验证...")
        
        # 检查订单数据必需字段
        required_fields = ['order_number', 'customer_name', 'product_type']
        for field in required_fields:
            if field not in self.order_data.columns:
                raise ValueError(f"订单数据缺少必需字段: {field}")
        
        # 检查重复订单号
        duplicate_orders = self.order_data[self.order_data['order_number'].duplicated()]
        if not duplicate_orders.empty:
            self._add_issue('order_duplicate', 'error', f"发现重复订单号: {duplicate_orders['order_number'].tolist()}")
        
        logger.info("数据验证完成")
    
    def _transform_data(self):
        """数据转换"""
        logger.info("正在进行数据转换...")
        
        # 处理订单数据
        self._transform_order_data()
        
        # 处理交易数据
        self._transform_transaction_data()
        
        # 处理客户数据
        if self.customer_data is not None:
            self._transform_customer_data()
        
        logger.info("数据转换完成")
    
    def _transform_order_data(self):
        """转换订单数据"""
        # 统计订单类型
        for _, order in self.order_data.iterrows():
            product_type = order.get('product_type', '').strip()
            periods = order.get('periods', 0)
            
            if product_type == '租赁':
                if periods == 4:
                    self.report_data['summary']['new_lease_orders'] += 1
                elif periods == 6:
                    self.report_data['summary']['old_lease_orders'] += 1
            elif product_type == '电商':
                self.report_data['summary']['ecommerce_orders'] += 1
        
        self.report_data['summary']['total_orders'] = len(self.order_data)
    
    def _transform_transaction_data(self):
        """转换交易数据"""
        self.report_data['summary']['total_transactions'] = len(self.transaction_data)
    
    def _transform_customer_data(self):
        """转换客户数据"""
        self.report_data['summary']['total_customers'] = len(self.customer_data)
    
    def _generate_payment_schedules(self):
        """生成还款计划"""
        logger.info("正在生成还款计划...")
        
        for _, order in self.order_data.iterrows():
            try:
                # 使用租赁规则引擎生成还款计划
                schedules = self.lease_engine.generate_payment_schedule(order, self.pricing_manager)
                
                # 将还款计划添加到订单数据中
                order_report = {
                    'order_number': order['order_number'],
                    'product_type': order.get('product_type', ''),
                    'periods': order.get('periods', 0),
                    'schedules': schedules
                }
                self.report_data['orders'].append(order_report)
                
            except Exception as e:
                self._add_issue(order['order_number'], 'error', f"生成还款计划失败: {str(e)}")
        
        logger.info("还款计划生成完成")
    
    def _calculate_payment_status(self):
        """计算账单状态"""
        logger.info("正在计算账单状态...")
        
        # 这里将实现账单状态计算逻辑
        # 聚合交易数据，计算每期实还金额和状态
        
        logger.info("账单状态计算完成")
    
    def _validate_contracts(self):
        """合同金额校验"""
        logger.info("正在进行合同金额校验...")
        
        # 这里将实现合同金额一致性校验逻辑
        
        logger.info("合同金额校验完成")
    
    def _load_to_database(self):
        """加载数据到数据库"""
        logger.info("正在写入数据库...")
        
        # 这里将实现数据库写入逻辑
        
        logger.info("数据库写入完成")
    
    def _finalize_report(self):
        """完成报告"""
        self.report_data['finished_at'] = datetime.utcnow().isoformat()
        
        # 保存报告到数据库
        self._save_report_to_database()
    
    def _save_report_to_database(self):
        """保存报告到数据库"""
        try:
            with get_db_session() as session:
                import_report = ImportReport(
                    run_id=self.run_id,
                    started_at=datetime.fromisoformat(self.report_data['started_at'].replace('Z', '+00:00')),
                    finished_at=datetime.fromisoformat(self.report_data['finished_at'].replace('Z', '+00:00')),
                    status='success' if 'error' not in self.report_data else 'failed',
                    total_orders=self.report_data['summary']['total_orders'],
                    total_transactions=self.report_data['summary']['total_transactions'],
                    total_customers=self.report_data['summary']['total_customers'],
                    new_lease_orders=self.report_data['summary']['new_lease_orders'],
                    old_lease_orders=self.report_data['summary']['old_lease_orders'],
                    ecommerce_orders=self.report_data['summary']['ecommerce_orders'],
                    used_pricing_count=self.report_data['summary']['used_pricing_count'],
                    used_reverse_calc_count=self.report_data['summary']['used_reverse_calc_count'],
                    contract_check_pass=self.report_data['summary']['contract_check_pass'],
                    contract_check_fail=self.report_data['summary']['contract_check_fail'],
                    report_data=str(self.report_data),
                    error_message=self.report_data.get('error')
                )
                session.add(import_report)
                session.commit()
                
        except Exception as e:
            logger.error(f"保存报告到数据库失败: {e}")
    
    def _add_issue(self, order_number: str, severity: str, message: str, issue_type: str = 'general'):
        """添加问题到报告"""
        issue = {
            'order_number': order_number,
            'type': issue_type,
            'severity': severity,
            'message': message
        }
        self.report_data['issues'].append(issue)
