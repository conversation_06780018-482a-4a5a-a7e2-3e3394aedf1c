#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ETL API路由
提供ETL相关的API接口
"""

import os
import json
import logging
from flask import Blueprint, request, jsonify, send_file
from werkzeug.utils import secure_filename
from datetime import datetime
from ..etl.etl_processor import ETLProcessor
from ..database.connection import get_db_session
from ..database.models import ImportReport
from ..config import UPLOAD_FOLDER, ALLOWED_EXTENSIONS

logger = logging.getLogger(__name__)

# 创建蓝图
etl_bp = Blueprint('etl', __name__, url_prefix='/api/etl')

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@etl_bp.route('/upload', methods=['POST'])
def upload_and_process():
    """
    上传Excel文件并执行ETL处理
    """
    try:
        # 检查文件
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': '没有上传文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': '没有选择文件'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'success': False, 'error': '不支持的文件格式'}), 400
        
        # 保存文件
        filename = secure_filename(file.filename)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{timestamp}_{filename}"
        file_path = os.path.join(UPLOAD_FOLDER, filename)
        file.save(file_path)
        
        # 获取参数
        dry_run = request.form.get('dry_run', 'false').lower() == 'true'
        
        # 执行ETL处理
        processor = ETLProcessor(file_path)
        report = processor.process(dry_run=dry_run)
        
        # 返回结果
        response = {
            'success': True,
            'run_id': report['run_id'],
            'summary': report['summary'],
            'report_url': f'/api/etl/report?run_id={report["run_id"]}'
        }
        
        # 如果报告较小，直接包含在响应中
        if len(str(report)) < 10000:  # 10KB限制
            response['report'] = report
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"ETL处理失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@etl_bp.route('/trigger', methods=['POST'])
def trigger_etl():
    """
    触发ETL处理（指定文件路径）
    """
    try:
        data = request.get_json()
        if not data or 'file_path' not in data:
            return jsonify({'success': False, 'error': '缺少file_path参数'}), 400
        
        file_path = data['file_path']
        if not os.path.exists(file_path):
            return jsonify({'success': False, 'error': '文件不存在'}), 400
        
        dry_run = data.get('dry_run', False)
        
        # 执行ETL处理
        processor = ETLProcessor(file_path)
        report = processor.process(dry_run=dry_run)
        
        # 返回结果
        response = {
            'success': True,
            'run_id': report['run_id'],
            'summary': report['summary'],
            'report_url': f'/api/etl/report?run_id={report["run_id"]}'
        }
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"ETL触发失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@etl_bp.route('/report', methods=['GET'])
def get_report():
    """
    获取导入报告
    """
    try:
        run_id = request.args.get('run_id')
        last = request.args.get('last', 'false').lower() == 'true'
        
        with get_db_session() as session:
            if last:
                # 获取最新报告
                report_record = session.query(ImportReport).order_by(
                    ImportReport.started_at.desc()
                ).first()
            elif run_id:
                # 获取指定run_id的报告
                report_record = session.query(ImportReport).filter(
                    ImportReport.run_id == run_id
                ).first()
            else:
                return jsonify({'success': False, 'error': '缺少run_id参数或last参数'}), 400
            
            if not report_record:
                return jsonify({'success': False, 'error': '报告不存在'}), 404
            
            # 解析报告数据
            try:
                report_data = json.loads(report_record.report_data) if report_record.report_data else {}
            except json.JSONDecodeError:
                report_data = {}
            
            response = {
                'success': True,
                'run_id': report_record.run_id,
                'report': report_data
            }
            
            return jsonify(response)
            
    except Exception as e:
        logger.error(f"获取报告失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@etl_bp.route('/report/download', methods=['GET'])
def download_report():
    """
    下载导入报告
    """
    try:
        run_id = request.args.get('run_id')
        last = request.args.get('last', 'false').lower() == 'true'
        format_type = request.args.get('format', 'json').lower()
        report_type = request.args.get('type', 'full')  # full, issues, schedules
        
        with get_db_session() as session:
            if last:
                report_record = session.query(ImportReport).order_by(
                    ImportReport.started_at.desc()
                ).first()
            elif run_id:
                report_record = session.query(ImportReport).filter(
                    ImportReport.run_id == run_id
                ).first()
            else:
                return jsonify({'success': False, 'error': '缺少run_id参数或last参数'}), 400
            
            if not report_record:
                return jsonify({'success': False, 'error': '报告不存在'}), 404
            
            # 解析报告数据
            try:
                report_data = json.loads(report_record.report_data) if report_record.report_data else {}
            except json.JSONDecodeError:
                report_data = {}
            
            # 生成文件
            if format_type == 'json':
                # JSON格式下载
                import tempfile
                with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                    json.dump(report_data, f, indent=2, ensure_ascii=False)
                    temp_path = f.name
                
                filename = f"import_report_{report_record.run_id}.json"
                return send_file(temp_path, as_attachment=True, download_name=filename)
                
            elif format_type == 'csv':
                # CSV格式下载
                import pandas as pd
                import tempfile
                
                if report_type == 'issues':
                    # 仅下载问题列表
                    issues_data = report_data.get('issues', [])
                    df = pd.DataFrame(issues_data)
                    filename = f"import_report_{report_record.run_id}_issues.csv"
                elif report_type == 'schedules':
                    # 下载还款计划明细
                    schedules_data = []
                    for order in report_data.get('orders', []):
                        for schedule in order.get('schedules', []):
                            schedule_row = {
                                'order_number': order['order_number'],
                                'product_type': order.get('product_type', ''),
                                'period': schedule.get('period', ''),
                                'due_date': schedule.get('due_date', ''),
                                'expected_amount': schedule.get('expected_amount', 0),
                                'paid_amount': schedule.get('paid_amount', 0),
                                'delta_amount': schedule.get('expected_amount', 0) - schedule.get('paid_amount', 0),
                                'status': schedule.get('status', ''),
                                'auto_inferred': schedule.get('auto_inferred', False)
                            }
                            schedules_data.append(schedule_row)
                    df = pd.DataFrame(schedules_data)
                    filename = f"import_report_{report_record.run_id}_schedules.csv"
                else:
                    return jsonify({'success': False, 'error': '不支持的CSV报告类型'}), 400
                
                with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig') as f:
                    df.to_csv(f, index=False)
                    temp_path = f.name
                
                return send_file(temp_path, as_attachment=True, download_name=filename)
            
            else:
                return jsonify({'success': False, 'error': '不支持的文件格式'}), 400
                
    except Exception as e:
        logger.error(f"下载报告失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@etl_bp.route('/metrics', methods=['GET'])
def get_metrics():
    """
    获取ETL指标
    """
    try:
        delta_threshold = float(request.args.get('delta_threshold', 100))
        source = request.args.get('source', 'latest')  # latest 或 db
        
        if source == 'latest':
            # 从最新报告聚合指标
            with get_db_session() as session:
                latest_report = session.query(ImportReport).order_by(
                    ImportReport.started_at.desc()
                ).first()
                
                if not latest_report:
                    return jsonify({'success': False, 'error': '没有可用的报告'}), 404
                
                try:
                    report_data = json.loads(latest_report.report_data) if latest_report.report_data else {}
                except json.JSONDecodeError:
                    report_data = {}
                
                # 计算Delta异常
                delta_anomaly_schedules = 0
                orders_with_anomaly = 0
                
                for order in report_data.get('orders', []):
                    order_has_anomaly = False
                    for schedule in order.get('schedules', []):
                        expected = schedule.get('expected_amount', 0)
                        paid = schedule.get('paid_amount', 0)
                        delta = abs(expected - paid)
                        if delta > delta_threshold:
                            delta_anomaly_schedules += 1
                            order_has_anomaly = True
                    if order_has_anomaly:
                        orders_with_anomaly += 1
                
                metrics = {
                    'total_orders': latest_report.total_orders,
                    'total_schedules': sum(len(order.get('schedules', [])) for order in report_data.get('orders', [])),
                    'contract_check_pass': latest_report.contract_check_pass,
                    'contract_check_fail': latest_report.contract_check_fail,
                    'delta_anomaly_schedules': delta_anomaly_schedules,
                    'orders_with_anomaly': orders_with_anomaly,
                    'overdue_orders': 0  # 需要从数据库实时计算
                }
                
                response = {
                    'success': True,
                    'run_id': latest_report.run_id,
                    'generated_at': datetime.utcnow().isoformat() + 'Z',
                    'delta_threshold': delta_threshold,
                    'metrics': metrics
                }
                
                return jsonify(response)
        
        else:
            # 从数据库实时扫描（暂未实现）
            return jsonify({'success': False, 'error': '数据库实时扫描暂未实现'}), 501
            
    except Exception as e:
        logger.error(f"获取指标失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500
