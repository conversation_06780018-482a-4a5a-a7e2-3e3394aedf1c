# Python虚拟环境
venv/
new_venv/
env/
ENV/
.env
.venv

# Python编译文件
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 日志文件
*.log
logs/

# 上传和处理的临时文件
bill-helper/uploads/
bill-helper/processed/

# 数据库文件
*.db
*.sqlite3
*.sqlite

# IDE相关文件
.idea/
.vscode/
*.swp
*.swo
.DS_Store
.project
.pydevproject
.settings/

# 临时文件
.tmp/
tmp/
temp/

# Excel临时文件
~$*.xlsx
~$*.xls

# 环境配置文件（可能包含敏感信息）
.env
.env.local
.env.development
.env.test
.env.production

# 个人配置文件
config.local.py

# 生成的文件
output/
results/

# 缓存文件
.cache/
.pytest_cache/
.coverage
htmlcov/

# Jupyter Notebook
.ipynb_checkpoints

# 其他
.DS_Store
Thumbs.db
